/*!
 * Bootstrap v4.6.2 中文本地化 (https://getbootstrap.com/)
 */
(function ($) {
  'use strict';

  // 定义中文本地化对象
  $.fn.tooltip.Constructor.Default.whiteList.span.push('data-notify');
  
  // 分页组件本地化
  if ($.fn.pagination) {
    $.fn.pagination.defaults.beforePageText = '第';
    $.fn.pagination.defaults.afterPageText = '页，共{pages}页';
    $.fn.pagination.defaults.displayMsg = '显示{from}到{to}，共{total}条记录';
  }
  
  // 日期选择器本地化
  if ($.fn.datepicker) {
    $.fn.datepicker.defaults.format = 'yyyy-mm-dd';
    $.fn.datepicker.defaults.weekStart = 1;
    $.fn.datepicker.defaults.autoclose = true;
    $.fn.datepicker.defaults.language = 'zh-CN';
  }
  
  // 表单验证消息本地化
  if ($.fn.validator) {
    $.fn.validator.Constructor.DEFAULTS.errors = {
      match: '输入与%s不匹配',
      minlength: '至少输入%s个字符',
      maxlength: '最多输入%s个字符',
      required: '必填字段',
      number: '请输入有效的数字',
      digits: '只能输入数字',
      email: '请输入有效的电子邮件地址'
    };
  }
  
  // 模态框本地化
  if ($.fn.modal) {
    $.fn.modal.Constructor.prototype._initializeBackDrop = function () {
      var backdrop = $(document.createElement('div'))
        .addClass('modal-backdrop fade')
        .appendTo(this.$element.parent());
      
      backdrop.on('click', $.proxy(function (e) {
        if (this.options.backdrop === 'static') {
          this.$element.trigger('focus');
        } else {
          this.hide();
        }
      }, this));
      
      if (this.options.backdrop !== 'static') {
        backdrop.append('<div class="modal-backdrop-close">×</div>');
        backdrop.find('.modal-backdrop-close').on('click', $.proxy(function () {
          this.hide();
        }, this));
      }
      
      this.$backdrop = backdrop;
    };
  }
  
  // 下拉菜单本地化
  if ($.fn.dropdown) {
    $.fn.dropdown.Constructor.prototype.keydown = function (e) {
      if (!/(38|40|27|32)/.test(e.which) || /input|textarea/i.test(e.target.tagName)) return;
      
      var $this = $(this);
      
      e.preventDefault();
      e.stopPropagation();
      
      if (this.disabled || $this.hasClass('disabled')) return;
      
      var $parent = getParent($this);
      var isActive = $parent.hasClass('open');
      
      if (!isActive && e.which != 27 || isActive && e.which == 27) {
        if (e.which == 27) $parent.find(toggle).trigger('focus');
        return $this.trigger('click');
      }
      
      var desc = ' li:not(.disabled):visible a';
      var $items = $parent.find('.dropdown-menu' + desc);
      
      if (!$items.length) return;
      
      var index = $items.index(e.target);
      
      if (e.which == 38 && index > 0) index--;
      if (e.which == 40 && index < $items.length - 1) index++;
      if (!~index) index = 0;
      
      $items.eq(index).trigger('focus');
    };
  }
  
  // 警告框本地化
  if ($.fn.alert) {
    $.fn.alert.Constructor.prototype.close = function (e) {
      var $this = $(this);
      var selector = $this.attr('data-target');
      
      if (!selector) {
        selector = $this.attr('href');
        selector = selector && selector.replace(/.*(?=#[^\s]*$)/, '');
      }
      
      var $parent = $(selector === '#' ? [] : selector);
      
      if (e) e.preventDefault();
      
      if (!$parent.length) {
        $parent = $this.closest('.alert');
      }
      
      $parent.trigger(e = $.Event('close.bs.alert'));
      
      if (e.isDefaultPrevented()) return;
      
      $parent.removeClass('in');
      
      function removeElement() {
        $parent.detach().trigger('closed.bs.alert').remove();
      }
      
      $.support.transition && $parent.hasClass('fade') ?
        $parent
          .one('bsTransitionEnd', removeElement)
          .emulateTransitionEnd(150) :
        removeElement();
    };
  }
  
})(jQuery);
