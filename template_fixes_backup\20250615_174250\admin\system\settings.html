{% extends 'base.html' %}

{% block title %}系统设置 - {{ super() }}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .settings-card {
        margin-bottom: 20px;
    }
    .settings-card .card-header {
        font-weight: bold;
    }
    .mb-3 label {
        font-weight: 500;
    }
    .setting-description {
        font-size: 0.85rem;
        color: #6c757d;
        margin-top: 5px;
    }
    .logo-upload-container {
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 1rem;
        background-color: #f8f9fa;
    }
    .logo-preview {
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 0.5rem;
        background-color: white;
        display: inline-block;
    }
    .logo-preview img {
        display: block;
    }
</style>

{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2>系统设置</h2>
        <p class="text-muted">配置系统的基本参数和行为</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('system.backups') }}" class="btn btn-info">
            <i class="fas fa-database"></i> 数据库备份
        </a>
        <a href="{{ url_for('system.monitor') }}" class="btn btn-primary">
            <i class="fas fa-chart-line"></i> 系统监控
        </a>
        <a href="{{ url_for('system.users') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回
        </a>
    </div>
</div>

<form method="post" action="{{ url_for('system.update_settings') }}" enctype="multipart/form-data" novalidate>
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

    <div class="row">
        <div class="col-md-3">
            <div class="list-group" id="settings-tab" role="tablist">
                {% for category in settings.keys() %}
                <a class="list-group-item list-group-item-action {% if loop.first %}active{% endif %}"
                   id="{{ category|lower|replace(' ', '-') }}-tab"
                   data-bs-toggle="list"
                   href="#{{ category|lower|replace(' ', '-') }}"
                   role="tab">
                    {{ category }}
                </a>
                {% endfor %}
            </div>
        </div>

        <div class="col-md-9">
            <div class="tab-content">
                {% for category, category_settings in settings.items() %}
                <div class="tab-pane fade {% if loop.first %}show active{% endif %}"
                     id="{{ category|lower|replace(' ', '-') }}"
                     role="tabpanel">

                    <div class="card settings-card">
                        <div class="card-header bg-primary text-white">
                            {{ category }}
                        </div>
                        <div class="card-body">
                            {% for setting in category_settings %}
                            <div class="mb-3">
                                <label for="setting_{{ setting.key }}">{{ setting.description }}</label>

                                {% if setting.key == 'project_name' %}
                                <input type="text" class="form-control" id="setting_{{ setting.key }}"
                                       name="setting_{{ setting.key }}" value="{{ setting.value }}">
                                <input type="hidden" name="value_type_{{ setting.key }}" value="{{ setting.value_type }}">

                                {% elif setting.key == 'theme_color' %}
                                <select class="form-control theme-color-selector" id="setting_{{ setting.key }}"
                                        name="setting_{{ setting.key }}" data-onchange="previewTheme(this.value)">
                                    <optgroup label="🎨 现代专业系列">
                                        <option value="primary" {% if setting.value == 'primary' %}selected{% endif %}>
                                            🌊 海洋蓝主题 - 专业、信任、稳定
                                        </option>
                                        <option value="secondary" {% if setting.value == 'secondary' %}selected{% endif %}>
                                            🔘 现代灰主题 - 简约、专业、平衡
                                        </option>
                                        <option value="success" {% if setting.value == 'success' %}selected{% endif %}>
                                            🌿 自然绿主题 - 健康、成长、和谐
                                        </option>
                                        <option value="warning" {% if setting.value == 'warning' %}selected{% endif %}>
                                            🔥 活力橙主题 - 活力、创新、温暖
                                        </option>
                                        <option value="info" {% if setting.value == 'info' %}selected{% endif %}>
                                            💜 优雅紫主题 - 创新、优雅、神秘
                                        </option>
                                        <option value="danger" {% if setting.value == 'danger' %}selected{% endif %}>
                                            ❤️ 深邃红主题 - 力量、重要、警示
                                        </option>
                                        <option value="dark" {% if setting.value == 'dark' %}selected{% endif %}>
                                            🌙 深色主题 - 现代、护眼、专业
                                        </option>
                                    </optgroup>
                                    <optgroup label="✨ 经典优雅系列">
                                        <option value="classic-neutral" {% if setting.value == 'classic-neutral' %}selected{% endif %}>
                                            🏛️ 经典中性风 - 米白色·深棕色·金色，温暖优雅
                                        </option>
                                        <option value="modern-neutral" {% if setting.value == 'modern-neutral' %}selected{% endif %}>
                                            🏢 现代中性风 - 灰白色·深灰色·香槟金，简洁高雅
                                        </option>
                                        <option value="noble-elegant" {% if setting.value == 'noble-elegant' %}selected{% endif %}>
                                            👑 贵族典雅风 - 藏青色·深紫色·银色，深邃高贵
                                        </option>
                                        <option value="royal-solemn" {% if setting.value == 'royal-solemn' %}selected{% endif %}>
                                            🎭 皇室庄重风 - 深紫色·深红色·黑色，神秘庄重
                                        </option>
                                    </optgroup>
                                    <optgroup label="🚀 DeepSeek 现代系列">
                                        <option value="deep-sea-tech" {% if setting.value == 'deep-sea-tech' %}selected{% endif %}>
                                            🌊 深海科技蓝 - 冷色调渐变设计，科技感与现代感，SaaS/数据类网站首选
                                        </option>
                                        <option value="soft-morandi" {% if setting.value == 'soft-morandi' %}selected{% endif %}>
                                            🎨 柔光莫兰迪 - 低饱和度撞色，柔和高级感，时尚创意行业
                                        </option>
                                        <option value="minimal-dawn" {% if setting.value == 'minimal-dawn' %}selected{% endif %}>
                                            🌅 极简晨曦 - 高对比暖色，视觉冲击力强，电商/活动页面
                                        </option>
                                        <option value="dark-neon" {% if setting.value == 'dark-neon' %}selected{% endif %}>
                                            🌃 暗夜霓虹 - 深色背景+亮色，赛博朋克风格，游戏/元宇宙主题
                                        </option>
                                        <option value="nature-eco" {% if setting.value == 'nature-eco' %}selected{% endif %}>
                                            🌿 自然生态绿 - 自然清新色调，治愈系风格，环保健康类网站
                                        </option>
                                    </optgroup>
                                </select>
                                <input type="hidden" name="value_type_{{ setting.key }}" value="{{ setting.value_type }}">
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        选择主题颜色后会实时预览效果，保存后全站生效
                                    </small>
                                </div>

                                {% elif setting.key == 'show_welcome_message' or setting.key.startswith('enable_') %}
                                <select class="form-control" id="setting_{{ setting.key }}"
                                        name="setting_{{ setting.key }}">
                                    <option value="1" {% if setting.value == '1' %}selected{% endif %}>是</option>
                                    <option value="0" {% if setting.value == '0' %}selected{% endif %}>否</option>
                                </select>
                                <input type="hidden" name="value_type_{{ setting.key }}" value="{{ setting.value_type }}">

                                {% elif setting.key == 'items_per_page' %}
                                <select class="form-control" id="setting_{{ setting.key }}"
                                        name="setting_{{ setting.key }}">
                                    <option value="10" {% if setting.value == '10' %}selected{% endif %}>10</option>
                                    <option value="20" {% if setting.value == '20' %}selected{% endif %}>20</option>
                                    <option value="50" {% if setting.value == '50' %}selected{% endif %}>50</option>
                                    <option value="100" {% if setting.value == '100' %}selected{% endif %}>100</option>
                                </select>
                                <input type="hidden" name="value_type_{{ setting.key }}" value="{{ setting.value_type }}">

                                {% elif setting.key == 'session_timeout' %}
                                <select class="form-control" id="setting_{{ setting.key }}"
                                        name="setting_{{ setting.key }}">
                                    <option value="1" {% if setting.value == '1' %}selected{% endif %}>1天</option>
                                    <option value="3" {% if setting.value == '3' %}selected{% endif %}>3天</option>
                                    <option value="7" {% if setting.value == '7' %}selected{% endif %}>7天</option>
                                    <option value="14" {% if setting.value == '14' %}selected{% endif %}>14天</option>
                                    <option value="30" {% if setting.value == '30' %}selected{% endif %}>30天</option>
                                </select>
                                <input type="hidden" name="value_type_{{ setting.key }}" value="{{ setting.value_type }}">

                                {% elif setting.key == 'system_logo' %}
                                <!-- 系统LOGO上传 -->
                                <div class="logo-upload-container">
                                    <!-- 当前LOGO预览 -->
                                    {% if setting.value %}
                                    <div class="current-logo mb-3">
                                        <label class="form-label">当前LOGO：</label>
                                        <div class="logo-preview">
                                            <img src="{{ setting.value }}" alt="系统LOGO" style="max-height: 50px; max-width: 150px;" class="img-thumbnail">
                                        </div>
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle"></i>
                                            此LOGO将同时用作网站图标（浏览器标签页图标）
                                        </small>
                                    </div>
                                    {% endif %}

                                    <!-- 文件上传 -->
                                    <div class="mb-3">
                                        <label for="logo_file" class="form-label">上传新LOGO：</label>
                                        <input type="file" class="form-control" id="logo_file" name="logo_file"
                                               accept="image/*" onchange="previewLogo(this)">
                                        <small class="form-text text-muted">
                                            支持 JPG、PNG、GIF 格式，建议尺寸：150x50像素，文件大小不超过2MB
                                        </small>
                                    </div>

                                    <!-- 新LOGO预览 -->
                                    <div id="new-logo-preview" style="display: none;">
                                        <label class="form-label">新LOGO预览：</label>
                                        <div class="logo-preview">
                                            <img id="preview-image" src="" alt="LOGO预览" style="max-height: 50px; max-width: 150px;" class="img-thumbnail">
                                        </div>
                                    </div>

                                    <!-- 隐藏的当前值 -->
                                    <input type="hidden" id="setting_{{ setting.key }}" name="setting_{{ setting.key }}" value="{{ setting.value }}">
                                    <input type="hidden" name="value_type_{{ setting.key }}" value="{{ setting.value_type }}">
                                </div>

                                {% else %}
                                <input type="text" class="form-control" id="setting_{{ setting.key }}"
                                       name="setting_{{ setting.key }}" value="{{ setting.value }}">
                                <input type="hidden" name="value_type_{{ setting.key }}" value="{{ setting.value_type }}">
                                {% endif %}

                                <div class="setting-description">{{ setting.description }}</div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-12 text-end">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> 保存设置
            </button>
        </div>
    </div>
</form>

{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
// 主题预览功能
function previewTheme(themeName) {
    if (window.themeSwitcher) {
        // 使用主题切换器的预览功能
        window.themeSwitcher.previewTheme(themeName);

        // 显示预览提示
        showThemePreviewToast(themeName);
    } else {
        // 如果主题切换器还没加载，直接应用主题
        document.documentElement.setAttribute('data-theme', themeName);
    }
}

// 显示主题预览提示
function showThemePreviewToast(themeName) {
    const themes = {
        // 现代专业系列
        'primary': '海洋蓝',
        'secondary': '现代灰',
        'success': '自然绿',
        'warning': '活力橙',
        'info': '优雅紫',
        'danger': '深邃红',
        'dark': '深色',
        // 经典优雅系列
        'classic-neutral': '经典中性风',
        'modern-neutral': '现代中性风',
        'noble-elegant': '贵族典雅风',
        'royal-solemn': '皇室庄重风',
        // DeepSeek 现代系列
        'deep-sea-tech': '深海科技蓝',
        'soft-morandi': '柔光莫兰迪',
        'minimal-dawn': '极简晨曦',
        'dark-neon': '暗夜霓虹',
        'nature-eco': '自然生态绿'
    };

    const themeName_zh = themes[themeName] || themeName;

    // 使用toastr显示提示
    if (typeof toastr !== 'undefined') {
        toastr.info(`正在预览 ${themeName_zh} 主题，3秒后恢复原主题`, '主题预览', {
            timeOut: 3000,
            progressBar: true
        });
    }
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 等待主题切换器加载完成
    setTimeout(function() {
        // 如果主题切换器已加载，同步当前选择的主题
        if (window.themeSwitcher) {
            const themeSelector = document.getElementById('setting_theme_color');
            if (themeSelector && themeSelector.value) {
                window.themeSwitcher.applyTheme(themeSelector.value);
            }
        }
    }, 500);

    // 监听主题选择器变化
    const themeSelector = document.getElementById('setting_theme_color');
    if (themeSelector) {
        themeSelector.addEventListener('change', function(e) {
            const selectedTheme = e.target.value;

            // 立即应用主题（不是预览）
            if (window.themeSwitcher) {
                window.themeSwitcher.applyTheme(selectedTheme);
            } else {
                document.documentElement.setAttribute('data-theme', selectedTheme);
            }

            // 显示应用提示
            if (typeof toastr !== 'undefined') {
                const themes = {
                    // 现代专业系列
                    'primary': '海洋蓝',
                    'secondary': '现代灰',
                    'success': '自然绿',
                    'warning': '活力橙',
                    'info': '优雅紫',
                    'danger': '深邃红',
                    'dark': '深色',
                    // 经典优雅系列
                    'classic-neutral': '经典中性风',
                    'modern-neutral': '现代中性风',
                    'noble-elegant': '贵族典雅风',
                    'royal-solemn': '皇室庄重风',
                    // DeepSeek 现代系列
                    'deep-sea-tech': '深海科技蓝',
                    'soft-morandi': '柔光莫兰迪',
                    'minimal-dawn': '极简晨曦',
                    'dark-neon': '暗夜霓虹',
                    'nature-eco': '自然生态绿'
                };
                const themeName_zh = themes[selectedTheme] || selectedTheme;
                toastr.success(`已切换到 ${themeName_zh} 主题`, '主题切换');
            }
        });
    }
});

// LOGO预览功能
function previewLogo(input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];

        // 检查文件大小（2MB限制）
        if (file.size > 2 * 1024 * 1024) {
            alert('文件大小不能超过2MB');
            input.value = '';
            return;
        }

        // 检查文件类型
        if (!file.type.match('image.*')) {
            alert('请选择图片文件');
            input.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            const imageUrl = e.target.result;
            document.getElementById('preview-image').src = imageUrl;
            document.getElementById('new-logo-preview').style.display = 'block';

            // 实时更新favicon预览
            updateFavicon(imageUrl);
        };
        reader.readAsDataURL(file);
    }
}

// 动态更新favicon
function updateFavicon(imageUrl) {
    // 移除现有的favicon
    const existingFavicons = document.querySelectorAll('link[rel*="icon"]');
    existingFavicons.forEach(favicon => favicon.remove());

    // 添加新的favicon
    const favicon = document.createElement('link');
    favicon.rel = 'icon';
    favicon.type = 'image/x-icon';
    favicon.href = imageUrl;
    document.head.appendChild(favicon);

    // 添加shortcut icon
    const shortcutIcon = document.createElement('link');
    shortcutIcon.rel = 'shortcut icon';
    shortcutIcon.type = 'image/x-icon';
    shortcutIcon.href = imageUrl;
    document.head.appendChild(shortcutIcon);

    // 添加apple-touch-icon
    const appleIcon = document.createElement('link');
    appleIcon.rel = 'apple-touch-icon';
    appleIcon.href = imageUrl;
    document.head.appendChild(appleIcon);
}

// 表单提交时的处理
document.addEventListener('DOMContentLoaded', function() {
    const settingsForm = document.querySelector('form');
    if (settingsForm) {
        settingsForm.addEventListener('submit', function(e) {
            // 表单提交时显示保存提示
            if (typeof toastr !== 'undefined') {
                toastr.info('正在保存设置...', '系统设置');
            }
        });
    }
});
</script>

{% endblock %}