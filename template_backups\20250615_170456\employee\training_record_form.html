{% extends 'base.html' %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h2>{{ title }}</h2>
        <p class="text-muted">为员工 {{ employee.name }} 添加培训记录</p>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <form method="post" enctype="multipart/form-data" novalidate>
            {{ form.hidden_tag() }}
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        {{ form.training_name.label }}
                        {{ form.training_name(class="form-control") }}
                        {% for error in form.training_name.errors %}
                        <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        {{ form.training_date.label }}
                        {{ form.training_date(class="form-control", type="date") }}
                        {% for error in form.training_date.errors %}
                        <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        {{ form.expire_date.label }}
                        {{ form.expire_date(class="form-control", type="date") }}
                        {% for error in form.expire_date.errors %}
                        <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                        <small class="form-text text-muted">如果培训证书长期有效，可不填</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        {{ form.certificate_no.label }}
                        {{ form.certificate_no(class="form-control") }}
                        {% for error in form.certificate_no.errors %}
                        <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        {{ form.score.label }}
                        {{ form.score(class="form-control", step="0.1") }}
                        {% for error in form.score.errors %}
                        <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        {{ form.trainer.label }}
                        {{ form.trainer(class="form-control") }}
                        {% for error in form.trainer.errors %}
                        <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                {{ form.certificate_img.label }}
                {{ form.certificate_img(class="form-control-file") }}
                {% for error in form.certificate_img.errors %}
                <small class="text-danger">{{ error }}</small>
                {% endfor %}
            </div>
            
            <div class="mb-3">
                {{ form.notes.label }}
                {{ form.notes(class="form-control", rows=3) }}
                {% for error in form.notes.errors %}
                <small class="text-danger">{{ error }}</small>
                {% endfor %}
            </div>
            
            <div class="mb-3 text-center">
                <a href="{{ url_for('employee.view_employee', id=employee.id) }}" class="btn btn-secondary">取消</a>
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}
