{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">确认产品信息</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('product_batch.index') }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <!-- 步骤进度条 -->
                            <div class="mb-4">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <small class="text-muted">创建产品批次流程</small>
                                    <small class="text-muted">步骤 5/5</small>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-success" role="progressbar" class="w-100" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <div class="d-flex justify-content-between mt-2">
                                    <small class="text-success">1. 基本信息</small>
                                    <small class="text-success">2. 选择食材</small>
                                    <small class="text-success">3. 设置属性</small>
                                    <small class="text-success">4. 个性调整</small>
                                    <small class="text-success fw-bold">5. 确认创建</small>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <h5><i class="icon fas fa-info"></i> 批次信息</h5>
                                <p>批次名称：{{ batch.name }}</p>
                                <p>分类：{{ batch.category.name if batch.category else '' }}</p>
                                <p>供应商：{{ batch.supplier.name if batch.supplier else '' }}</p>
                                <p>产品数量：{{ product_data|length }}</p>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>食材名称</th>
                                            <th>价格(元)</th>
                                            <th>规格</th>
                                            <th>质量认证</th>
                                            <th>供货周期(天)</th>
                                            <th>最小订购量</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for product in product_data %}
                                        <tr>
                                            <td>{{ ingredients[product.ingredient_id].name if product.ingredient_id in ingredients else product.product_name }}</td>
                                            <td>{{ product.price }}</td>
                                            <td>{{ product.specification_value }} {{ units[product.unit_id].symbol if product.unit_id in units else '' }}</td>
                                            <td>{{ product.quality_cert }}</td>
                                            <td>{{ product.lead_time }}</td>
                                            <td>{{ product.min_order_quantity }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>

                            <form method="post" class="mt-3" novalidate>
                                {{ form.csrf_token }}
                                {{ form.batch_id }}

                                <div class="alert alert-warning">
                                    <h5><i class="icon fas fa-exclamation-triangle"></i> 确认提示</h5>
                                    <p>请确认以上产品信息无误，提交后将创建 {{ product_data|length }} 个产品，并等待审核。</p>
                                </div>

                                <div class="mb-3 text-center">
                                    {{ form.submit(class="btn btn-primary") }}
                                    <a href="{{ url_for('product_batch.adjust_products', id=batch.id) }}" class="btn btn-default">返回上一步</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
