#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模板修复专业工具
===============

专门修复StudentsCMSSP项目中的模板问题

功能特性:
1. 修复重复CSRF令牌问题
2. Bootstrap 4到5的语法升级
3. 修复HTML结构问题
4. 优化表单验证
5. 修复安全问题
6. 生成详细修复报告
"""

import os
import re
import json
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple
import logging


class TemplateFixerPro:
    """专业模板修复工具"""
    
    def __init__(self, project_root="."):
        self.project_root = Path(project_root).resolve()
        self.templates_root = self.project_root / "app" / "templates"
        
        # 修复规则定义
        self.fix_rules = {
            # CSRF令牌修复 - 只修复已存在的错误，不添加新的
            "csrf_fixes": [
                {
                    "name": "重复CSRF令牌",
                    "pattern": r'(\s*)\{\{\s*csrf_token\(\)\s*\}\}\s*\n(\s*)\{\{\s*form\.hidden_tag\(\)\s*\}\}',
                    "replacement": r'\2{{ form.hidden_tag() }}',
                    "description": "移除重复的csrf_token()，保留form.hidden_tag()"
                },
                {
                    "name": "重复CSRF令牌(反向)",
                    "pattern": r'(\s*)\{\{\s*form\.hidden_tag\(\)\s*\}\}\s*\n(\s*)\{\{\s*csrf_token\(\)\s*\}\}',
                    "replacement": r'\1{{ form.hidden_tag() }}',
                    "description": "移除重复的csrf_token()，保留form.hidden_tag()"
                },
                {
                    "name": "修复错误的CSRF语法",
                    "pattern": r'\{\{\s*csrf_token\s*\}\}',
                    "replacement": r'{{ csrf_token() }}',
                    "description": "修复csrf_token语法错误（缺少括号）"
                },
                {
                    "name": "清理多余空白的CSRF",
                    "pattern": r'\{\{\s{2,}csrf_token\(\)\s{2,}\}\}',
                    "replacement": r'{{ csrf_token() }}',
                    "description": "清理CSRF令牌周围的多余空白"
                }
            ],
            
            # Bootstrap 4到5升级
            "bootstrap_fixes": [
                {
                    "name": "custom-control到form-check",
                    "pattern": r'<div class="custom-control custom-checkbox">',
                    "replacement": '<div class="form-check">',
                    "description": "Bootstrap 5: custom-control → form-check"
                },
                {
                    "name": "custom-control-input到form-check-input",
                    "pattern": r'class="custom-control-input"',
                    "replacement": 'class="form-check-input"',
                    "description": "Bootstrap 5: custom-control-input → form-check-input"
                },
                {
                    "name": "custom-control-label到form-check-label",
                    "pattern": r'class="custom-control-label"',
                    "replacement": 'class="form-check-label"',
                    "description": "Bootstrap 5: custom-control-label → form-check-label"
                },
                {
                    "name": "btn-block到w-100",
                    "pattern": r'\bbtn-block\b',
                    "replacement": 'w-100',
                    "description": "Bootstrap 5: btn-block → w-100"
                },
                {
                    "name": "text-left到text-start",
                    "pattern": r'\btext-left\b',
                    "replacement": 'text-start',
                    "description": "Bootstrap 5: text-left → text-start"
                },
                {
                    "name": "text-right到text-end",
                    "pattern": r'\btext-right\b',
                    "replacement": 'text-end',
                    "description": "Bootstrap 5: text-right → text-end"
                },
                {
                    "name": "float-left到float-start",
                    "pattern": r'\bfloat-left\b',
                    "replacement": 'float-start',
                    "description": "Bootstrap 5: float-left → float-start"
                },
                {
                    "name": "float-right到float-end",
                    "pattern": r'\bfloat-right\b',
                    "replacement": 'float-end',
                    "description": "Bootstrap 5: float-right → float-end"
                },
                {
                    "name": "ml-到ms-",
                    "pattern": r'\bml-(\d+)\b',
                    "replacement": r'ms-\1',
                    "description": "Bootstrap 5: ml-* → ms-*"
                },
                {
                    "name": "mr-到me-",
                    "pattern": r'\bmr-(\d+)\b',
                    "replacement": r'me-\1',
                    "description": "Bootstrap 5: mr-* → me-*"
                },
                {
                    "name": "pl-到ps-",
                    "pattern": r'\bpl-(\d+)\b',
                    "replacement": r'ps-\1',
                    "description": "Bootstrap 5: pl-* → ps-*"
                },
                {
                    "name": "pr-到pe-",
                    "pattern": r'\bpr-(\d+)\b',
                    "replacement": r'pe-\1',
                    "description": "Bootstrap 5: pr-* → pe-*"
                },
                {
                    "name": "form-group到mb-3",
                    "pattern": r'\bform-group\b',
                    "replacement": 'mb-3',
                    "description": "Bootstrap 5: form-group → mb-3"
                },
                {
                    "name": "sr-only到visually-hidden",
                    "pattern": r'\bsr-only\b',
                    "replacement": 'visually-hidden',
                    "description": "Bootstrap 5: sr-only → visually-hidden"
                },
                {
                    "name": "no-gutters到g-0",
                    "pattern": r'\bno-gutters\b',
                    "replacement": 'g-0',
                    "description": "Bootstrap 5: no-gutters → g-0"
                }
            ],
            
            # HTML结构优化
            "html_structure_fixes": [
                {
                    "name": "简化input-group结构",
                    "pattern": r'<div class="input-group">\s*<div class="">\s*<span class="input-group-text">',
                    "replacement": '<div class="input-group">\n                            <span class="input-group-text">',
                    "description": "移除input-group中的空div"
                },
                {
                    "name": "修复空class属性",
                    "pattern": r'class=""',
                    "replacement": '',
                    "description": "移除空的class属性"
                }
            ],
            
            # 表单验证优化
            "form_validation_fixes": [
                {
                    "name": "添加novalidate属性",
                    "pattern": r'<form([^>]*method\s*=\s*["\']post["\'][^>]*)>',
                    "replacement": r'<form\1 novalidate>',
                    "description": "为POST表单添加novalidate属性"
                }
            ],
            
            # 安全性检查（智能判断）
            "security_checks": [
                {
                    "name": "检查可能需要CSRF保护的表单",
                    "pattern": r'<form[^>]*method\s*=\s*["\']post["\'][^>]*>(?![^<]*(?:csrf_token|hidden_tag))',
                    "replacement": None,  # 只检查不修复
                    "description": "检测可能需要CSRF保护的POST表单",
                    "check_context": True  # 需要上下文判断
                }
            ],

            # 不需要CSRF保护的表单类型
            "csrf_exceptions": [
                "search",      # 搜索表单
                "filter",      # 过滤表单
                "sort",        # 排序表单
                "pagination",  # 分页表单
                "export",      # 导出表单（某些情况）
                "public"       # 公开表单（如联系我们）
            ]
        }
        
        # 修复结果
        self.fix_results = {
            "timestamp": datetime.now().isoformat(),
            "project_root": str(self.project_root),
            "files_processed": [],
            "fixes_applied": [],
            "issues_found": [],
            "statistics": {
                "total_files": 0,
                "files_modified": 0,
                "total_fixes": 0,
                "csrf_fixes": 0,
                "bootstrap_fixes": 0,
                "html_fixes": 0,
                "form_fixes": 0,
                "security_issues": 0
            }
        }
        
        # 设置日志
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def scan_template_files(self) -> List[Path]:
        """扫描模板文件"""
        if not self.templates_root.exists():
            self.logger.error(f"❌ 模板目录不存在: {self.templates_root}")
            return []
        
        template_files = list(self.templates_root.rglob("*.html"))
        self.fix_results["statistics"]["total_files"] = len(template_files)
        self.logger.info(f"📁 发现 {len(template_files)} 个模板文件")
        
        return template_files
    
    def create_backup(self, file_path: Path) -> Path:
        """创建文件备份"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir = self.project_root / "template_fixes_backup" / timestamp
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        relative_path = file_path.relative_to(self.templates_root)
        backup_file = backup_dir / relative_path
        backup_file.parent.mkdir(parents=True, exist_ok=True)
        
        shutil.copy2(file_path, backup_file)
        return backup_file
    
    def apply_fixes_to_file(self, file_path: Path) -> Dict:
        """对单个文件应用修复"""
        file_result = {
            "file": str(file_path.relative_to(self.project_root)),
            "backup_created": False,
            "fixes_applied": [],
            "issues_found": [],
            "total_changes": 0
        }
        
        try:
            # 读取文件
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            modified_content = original_content
            total_fixes = 0
            
            # 应用CSRF修复
            for fix in self.fix_rules["csrf_fixes"]:
                new_content, count = self._apply_fix(modified_content, fix)
                if count > 0:
                    modified_content = new_content
                    total_fixes += count
                    self.fix_results["statistics"]["csrf_fixes"] += count
                    file_result["fixes_applied"].append({
                        "type": "csrf",
                        "name": fix["name"],
                        "count": count,
                        "description": fix["description"]
                    })
            
            # 应用Bootstrap修复
            for fix in self.fix_rules["bootstrap_fixes"]:
                new_content, count = self._apply_fix(modified_content, fix)
                if count > 0:
                    modified_content = new_content
                    total_fixes += count
                    self.fix_results["statistics"]["bootstrap_fixes"] += count
                    file_result["fixes_applied"].append({
                        "type": "bootstrap",
                        "name": fix["name"],
                        "count": count,
                        "description": fix["description"]
                    })
            
            # 应用HTML结构修复
            for fix in self.fix_rules["html_structure_fixes"]:
                new_content, count = self._apply_fix(modified_content, fix)
                if count > 0:
                    modified_content = new_content
                    total_fixes += count
                    self.fix_results["statistics"]["html_fixes"] += count
                    file_result["fixes_applied"].append({
                        "type": "html_structure",
                        "name": fix["name"],
                        "count": count,
                        "description": fix["description"]
                    })
            
            # 应用表单验证修复
            for fix in self.fix_rules["form_validation_fixes"]:
                new_content, count = self._apply_fix(modified_content, fix)
                if count > 0:
                    modified_content = new_content
                    total_fixes += count
                    self.fix_results["statistics"]["form_fixes"] += count
                    file_result["fixes_applied"].append({
                        "type": "form_validation",
                        "name": fix["name"],
                        "count": count,
                        "description": fix["description"]
                    })
            
            # 检查安全问题（智能CSRF检查）
            for fix in self.fix_rules["security_checks"]:
                issues = self._check_csrf_intelligently(modified_content, fix, file_path)
                if issues:
                    file_result["issues_found"].extend(issues)
                    self.fix_results["statistics"]["security_issues"] += len(issues)
            
            file_result["total_changes"] = total_fixes
            
            # 如果有修改，保存文件
            if modified_content != original_content:
                # 创建备份
                backup_file = self.create_backup(file_path)
                file_result["backup_created"] = True
                
                # 保存修改后的文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(modified_content)
                
                self.fix_results["statistics"]["files_modified"] += 1
                self.fix_results["statistics"]["total_fixes"] += total_fixes
                
                self.logger.info(f"✅ 修复完成: {file_path.relative_to(self.templates_root)} ({total_fixes} 处修改)")
            
        except Exception as e:
            error_msg = f"修复文件失败: {str(e)}"
            file_result["issues_found"].append({
                "type": "file_error",
                "message": error_msg,
                "severity": "error"
            })
            self.logger.error(f"❌ {file_path}: {error_msg}")
        
        return file_result
    
    def _apply_fix(self, content: str, fix: Dict) -> Tuple[str, int]:
        """应用单个修复规则"""
        if fix["replacement"] is None:
            return content, 0
        
        pattern = fix["pattern"]
        replacement = fix["replacement"]
        
        # 计算匹配次数
        matches = re.findall(pattern, content)
        count = len(matches)
        
        if count > 0:
            # 应用替换
            new_content = re.sub(pattern, replacement, content)
            return new_content, count
        
        return content, 0
    
    def _check_csrf_intelligently(self, content: str, fix: Dict, file_path: Path) -> List[Dict]:
        """智能检查CSRF保护 - 豁免策略"""
        issues = []

        # 查找所有POST表单
        post_forms = list(re.finditer(r'<form[^>]*method\s*=\s*["\']post["\'][^>]*>', content, re.IGNORECASE))

        for form_match in post_forms:
            line_num = content[:form_match.start()].count('\n') + 1
            form_tag = form_match.group()

            # 检查表单后面的内容，看是否有CSRF保护
            form_start = form_match.end()
            # 查看表单标签后的500个字符
            form_content = content[form_start:form_start + 500]

            has_csrf = bool(re.search(r'csrf_token|hidden_tag', form_content, re.IGNORECASE))

            if has_csrf:
                # 如果有CSRF保护，检查是否有错误
                csrf_errors = self._check_csrf_errors(form_content)
                if csrf_errors:
                    issues.append({
                        "type": "csrf_error",
                        "name": "CSRF令牌错误",
                        "message": f"第{line_num}行的表单存在CSRF令牌错误: {csrf_errors}",
                        "line": line_num,
                        "severity": "warning",
                        "action": "fix_existing"
                    })
            else:
                # 没有CSRF保护 - 检查是否应该豁免
                should_exempt = self._should_exempt_csrf(form_tag, file_path)
                if should_exempt:
                    issues.append({
                        "type": "csrf_exempt",
                        "name": "CSRF豁免表单",
                        "message": f"第{line_num}行的表单已豁免CSRF保护 (搜索/过滤/公开表单)",
                        "line": line_num,
                        "severity": "info",
                        "action": "exempt"
                    })
                else:
                    issues.append({
                        "type": "csrf_missing",
                        "name": "缺少CSRF保护",
                        "message": f"第{line_num}行的表单可能需要CSRF保护 (请手动确认)",
                        "line": line_num,
                        "severity": "warning",
                        "action": "manual_review"
                    })

        return issues

    def _check_csrf_errors(self, form_content: str) -> str:
        """检查CSRF令牌错误"""
        errors = []

        # 检查重复的CSRF令牌
        csrf_count = len(re.findall(r'csrf_token\(\)', form_content))
        hidden_tag_count = len(re.findall(r'hidden_tag\(\)', form_content))

        if csrf_count > 0 and hidden_tag_count > 0:
            errors.append("重复的CSRF令牌")

        if csrf_count > 1:
            errors.append("多个csrf_token()")

        if hidden_tag_count > 1:
            errors.append("多个hidden_tag()")

        # 检查语法错误
        if re.search(r'csrf_token\s*\}', form_content):  # 缺少括号
            errors.append("csrf_token语法错误")

        return "; ".join(errors)

    def _should_exempt_csrf(self, form_tag: str, file_path: Path) -> bool:
        """判断是否应该豁免CSRF保护"""
        form_tag_lower = form_tag.lower()
        file_name = file_path.name.lower()

        # 检查表单属性中的豁免关键词
        exempt_keywords = [
            'search', 'filter', 'sort', 'pagination', 'export',
            'public', 'contact', 'newsletter', 'subscribe'
        ]

        for keyword in exempt_keywords:
            if keyword in form_tag_lower or keyword in file_name:
                return True

        # 检查特定的文件路径
        exempt_paths = [
            'public', 'search', 'filter', 'contact', 'newsletter'
        ]

        for path_part in exempt_paths:
            if path_part in str(file_path).lower():
                return True

        # 检查GET方法的表单（虽然这里是POST，但可能是误用）
        if 'action' in form_tag_lower and any(word in form_tag_lower for word in ['search', 'filter']):
            return True

        return False
    
    def run_template_fixes(self) -> Dict:
        """运行模板修复"""
        self.logger.info("🚀 开始模板修复...")
        
        # 扫描模板文件
        template_files = self.scan_template_files()
        
        if not template_files:
            self.logger.warning("⚠️ 未找到模板文件")
            return self.fix_results
        
        # 修复每个文件
        for i, file_path in enumerate(template_files, 1):
            self.logger.info(f"🔧 [{i}/{len(template_files)}] 修复: {file_path.relative_to(self.templates_root)}")
            
            file_result = self.apply_fixes_to_file(file_path)
            self.fix_results["files_processed"].append(file_result)
            
            if file_result["fixes_applied"]:
                self.fix_results["fixes_applied"].append(file_result)
            
            if file_result["issues_found"]:
                self.fix_results["issues_found"].extend(file_result["issues_found"])
        
        self.logger.info("✅ 模板修复完成")
        return self.fix_results
    
    def generate_fix_report(self) -> str:
        """生成修复报告"""
        stats = self.fix_results["statistics"]
        
        report = []
        report.append("=" * 80)
        report.append("🔧 模板修复专业工具报告")
        report.append("=" * 80)
        report.append(f"修复时间: {self.fix_results['timestamp'][:19]}")
        report.append(f"项目路径: {self.fix_results['project_root']}")
        report.append("")
        
        # 修复统计
        report.append("📊 修复统计:")
        report.append(f"  • 处理文件: {stats['total_files']} 个")
        report.append(f"  • 修改文件: {stats['files_modified']} 个")
        report.append(f"  • 总修复数: {stats['total_fixes']} 处")
        report.append("")
        
        # 分类统计
        report.append("🔍 修复分类:")
        report.append(f"  • CSRF令牌修复: {stats['csrf_fixes']} 处")
        report.append(f"  • Bootstrap升级: {stats['bootstrap_fixes']} 处")
        report.append(f"  • HTML结构优化: {stats['html_fixes']} 处")
        report.append(f"  • 表单验证优化: {stats['form_fixes']} 处")
        report.append(f"  • 安全问题发现: {stats['security_issues']} 个")
        report.append("")
        
        # 修复详情
        if self.fix_results["fixes_applied"]:
            report.append("✅ 修复的文件:")
            for file_result in self.fix_results["fixes_applied"]:
                report.append(f"\n📄 {file_result['file']}:")
                for fix in file_result["fixes_applied"]:
                    report.append(f"  • {fix['name']}: {fix['count']} 处 - {fix['description']}")
        
        # 发现的问题
        if self.fix_results["issues_found"]:
            report.append("\n⚠️ 发现的问题:")
            for issue in self.fix_results["issues_found"]:
                severity_icon = {"error": "❌", "warning": "⚠️", "info": "ℹ️"}.get(issue["severity"], "•")
                if "line" in issue:
                    report.append(f"  {severity_icon} 第{issue['line']}行: {issue['message']}")
                else:
                    report.append(f"  {severity_icon} {issue['message']}")
        
        report.append("")
        report.append("🎉 修复完成!")
        report.append("建议:")
        report.append("1. 测试所有修复的页面功能")
        report.append("2. 检查Bootstrap 5的新特性")
        report.append("3. 验证表单提交和CSRF保护")
        report.append("4. 检查响应式布局")
        
        report.append("")
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def save_results(self):
        """保存修复结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存详细JSON报告
        json_file = self.project_root / f"template_fix_report_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.fix_results, f, indent=2, ensure_ascii=False)
        
        # 保存文本报告
        text_file = self.project_root / f"template_fix_summary_{timestamp}.txt"
        summary_report = self.generate_fix_report()
        with open(text_file, 'w', encoding='utf-8') as f:
            f.write(summary_report)
        
        self.logger.info(f"📊 详细报告已保存: {json_file}")
        self.logger.info(f"📊 摘要报告已保存: {text_file}")
        
        # 显示摘要
        print(summary_report)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="模板修复专业工具")
    parser.add_argument("--project-root", default=".", help="项目根目录路径")
    parser.add_argument("--dry-run", action="store_true", help="只检查不修复")
    
    args = parser.parse_args()
    
    # 创建修复器
    fixer = TemplateFixerPro(args.project_root)
    
    if args.dry_run:
        print("🔍 运行检查模式（不会修改文件）...")
        # TODO: 实现dry-run模式
    
    # 运行修复
    results = fixer.run_template_fixes()
    
    # 保存结果
    fixer.save_results()
    
    # 返回状态
    if results["statistics"]["total_fixes"] > 0:
        print(f"\n🎉 成功修复 {results['statistics']['total_fixes']} 个问题")
    else:
        print("\n✅ 未发现需要修复的问题")


if __name__ == "__main__":
    main()
