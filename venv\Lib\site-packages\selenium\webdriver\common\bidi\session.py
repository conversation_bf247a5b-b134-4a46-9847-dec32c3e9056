# Licensed to the Software Freedom Conservancy (SFC) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The SFC licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.


def session_subscribe(*events, browsing_contexts=None):
    cmd_dict = {
        "method": "session.subscribe",
        "params": {
            "events": events,
        },
    }
    if browsing_contexts is None:
        browsing_contexts = []
    if browsing_contexts:
        cmd_dict["params"]["browsingContexts"] = browsing_contexts
    _ = yield cmd_dict
    return None


def session_unsubscribe(*events, browsing_contexts=None):
    cmd_dict = {
        "method": "session.unsubscribe",
        "params": {
            "events": events,
        },
    }
    if browsing_contexts is None:
        browsing_contexts = []
    if browsing_contexts:
        cmd_dict["params"]["browsingContexts"] = browsing_contexts
    _ = yield cmd_dict
    return None
