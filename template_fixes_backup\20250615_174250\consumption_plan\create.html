{% extends 'base.html' %}

{% block title %}创建消耗计划{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
    .ingredient-checkbox:checked + label:after {
        background-color: #007bff;
    }
    .condiment-row {
        transition: all 0.3s ease;
    }
    .highlight-row {
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">创建消耗计划</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('consumption_plan.index') }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回消耗计划列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th class="w-30">菜单ID</th>
                                    <td>{{ menu_plan.id }}</td>
                                </tr>
                                <tr>
                                    <th>区域</th>
                                    <td>{{ menu_plan.area.name }}</td>
                                </tr>
                                <tr>
                                    <th>计划日期</th>
                                    <td>{{ menu_plan.plan_date }}</td>
                                </tr>
                                <tr>
                                    <th>餐次</th>
                                    <td>{{ menu_plan.meal_type }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th class="w-30">预计用餐人数</th>
                                    <td>{{ menu_plan.expected_diners or '-' }}</td>
                                </tr>
                                <tr>
                                    <th>状态</th>
                                    <td>{{ menu_plan.status }}</td>
                                </tr>
                                <tr>
                                    <th>创建人</th>
                                    <td>{{ menu_plan.creator.real_name or menu_plan.creator.username }}</td>
                                </tr>
                                <tr>
                                    <th>审批人</th>
                                    <td>{{ menu_plan.approver.real_name or menu_plan.approver.username if menu_plan.approver else '-' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- 菜单食谱列表 -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h4 class="card-title">菜单食谱列表</h4>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>食谱名称</th>
                                            <th>计划数量</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for menu_recipe in menu_recipes %}
                                        <tr>
                                            <td>{{ menu_recipe.recipe.name }}</td>
                                            <td>{{ menu_recipe.planned_quantity }}</td>
                                        </tr>
                                        {% else %}
                                        <tr>
                                            <td colspan="2" class="text-center">暂无食谱</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 仓库食材选择 -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h4 class="card-title">仓库食材选择</h4>
                            <div class="card-tools">
                                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                    <i class="fas fa-minus"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> 请选择需要消耗的主要食材，调味品可在下方自由添加。
                            </div>

                            <!-- 食材分类选项卡 -->
                            <ul class="nav nav-tabs" id="ingredient-tabs" role="tablist">
                                {% for category in ingredient_categories %}
                                <li class="nav-item">
                                    <a class="nav-link {% if loop.first %}active{% endif %}"
                                       id="category-{{ category.id }}-tab"
                                       data-bs-toggle="tab"
                                       href="#category-{{ category.id }}"
                                       role="tab">
                                        {{ category.name }}
                                    </a>
                                </li>
                                {% endfor %}
                            </ul>

                            <!-- 食材列表 -->
                            <div class="tab-content mt-3" id="ingredient-content">
                                {% for category in ingredient_categories %}
                                <div class="tab-pane fade {% if loop.first %}show active{% endif %}"
                                     id="category-{{ category.id }}"
                                     role="tabpanel">

                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr>
                                                    <th style="width: 40px">选择</th>
                                                    <th>食材名称</th>
                                                    <th>库存数量</th>
                                                    <th>单位</th>
                                                    <th>计划消耗数量</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for inventory in category_inventories.get(category.id, []) %}
                                                <tr>
                                                    <td>
                                                        <div class="icheck-primary">
                                                            <input type="checkbox"
                                                                   id="ingredient-{{ inventory.ingredient_id }}"
                                                                   name="selected_ingredients"
                                                                   value="{{ inventory.ingredient_id }}"
                                                                   class="ingredient-checkbox">
                                                            <label for="ingredient-{{ inventory.ingredient_id }}"></label>
                                                        </div>
                                                    </td>
                                                    <td>{{ inventory.ingredient.name }}</td>
                                                    <td>{{ inventory.total_quantity }}</td>
                                                    <td>{{ inventory.unit }}</td>
                                                    <td>
                                                        <input type="number"
                                                               name="quantity_{{ inventory.ingredient_id }}"
                                                               class="form-control ingredient-quantity"
                                                               step="0.01"
                                                               min="0"
                                                               max="{{ inventory.total_quantity }}"
                                                               disabled>
                                                    </td>
                                                </tr>
                                                {% else %}
                                                <tr>
                                                    <td colspan="5" class="text-center">该分类下暂无库存食材</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>

                    <!-- 调味品和其他消耗 -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h4 class="card-title">调味品和其他消耗</h4>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> 可自由添加调味品和其他消耗项目。
                            </div>

                            <div id="condiments-container">
                                <div class="row condiment-row mb-2">
                                    <div class="col-md-4">
                                        <input type="text" name="condiment_name[]" class="form-control" placeholder="名称">
                                    </div>
                                    <div class="col-md-3">
                                        <input type="number" name="condiment_quantity[]" class="form-control" placeholder="数量" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-3">
                                        <input type="text" name="condiment_unit[]" class="form-control" placeholder="单位">
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-danger remove-condiment">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <button type="button" id="add-condiment" class="btn btn-success mt-2">
                                <i class="fas fa-plus"></i> 添加调味品
                            </button>
                        </div>
                    </div>

                    <!-- 创建消耗计划表单 -->
                    <form method="post" action="{{ url_for('consumption_plan.create', menu_plan_id=menu_plan.id) }}" class="mt-4" id="consumption-form" novalidate>
                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">消耗计划信息</h4>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="consumption_date">消耗日期</label>
                                    <input type="date" name="consumption_date" id="consumption_date" class="form-control" value="{{ today_date }}" required>
                                </div>

                                <div class="mb-3">
                                    <label for="meal_type">餐次</label>
                                    <select name="meal_type" id="meal_type" class="form-control" required>
                                        <option value="早餐" {% if menu_plan.meal_type == '早餐' %}selected{% endif %}>早餐</option>
                                        <option value="午餐" {% if menu_plan.meal_type == '午餐' %}selected{% endif %}>午餐</option>
                                        <option value="晚餐" {% if menu_plan.meal_type == '晚餐' %}selected{% endif %}>晚餐</option>
                                        <option value="加餐">加餐</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="diners_count">用餐人数</label>
                                    <input type="number" name="diners_count" id="diners_count" class="form-control" value="{{ menu_plan.expected_diners or '' }}" min="1">
                                </div>

                                <div class="mb-3">
                                    <label for="notes">备注</label>
                                    <textarea name="notes" id="notes" class="form-control" rows="3"></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3 text-center mt-4">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save"></i> 创建消耗计划
                            </button>
                            <a href="{{ url_for('consumption_plan.index') }}" class="btn btn-default btn-lg">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>

                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 处理食材选择
        $('.ingredient-checkbox').change(function() {
            var ingredientId = $(this).val();
            var quantityInput = $('input[name="quantity_' + ingredientId + '"]');

            if ($(this).is(':checked')) {
                quantityInput.prop('disabled', false).focus();
            } else {
                quantityInput.prop('disabled', true).val('');
            }
        });

        // 添加调味品行
        $('#add-condiment').click(function() {
            var newRow = `
                <div class="row condiment-row mb-2 highlight-row">
                    <div class="col-md-4">
                        <input type="text" name="condiment_name[]" class="form-control" placeholder="名称">
                    </div>
                    <div class="col-md-3">
                        <input type="number" name="condiment_quantity[]" class="form-control" placeholder="数量" step="0.01" min="0">
                    </div>
                    <div class="col-md-3">
                        <input type="text" name="condiment_unit[]" class="form-control" placeholder="单位">
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-danger remove-condiment">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;

            $('#condiments-container').append(newRow);

            // 添加后移除高亮效果
            setTimeout(function() {
                $('.highlight-row').removeClass('highlight-row');
            }, 1000);
        });

        // 删除调味品行
        $(document).on('click', '.remove-condiment', function() {
            $(this).closest('.condiment-row').fadeOut(300, function() {
                $(this).remove();
            });
        });

        // 表单提交前验证
        $('#consumption-form').submit(function(e) {
            var hasSelectedIngredients = false;

            // 检查是否选择了至少一个食材
            $('.ingredient-checkbox:checked').each(function() {
                var ingredientId = $(this).val();
                var quantity = $('input[name="quantity_' + ingredientId + '"]').val();

                if (quantity && quantity > 0) {
                    hasSelectedIngredients = true;
                    return false; // 跳出循环
                }
            });

            // 检查是否添加了至少一个调味品
            var hasCondiments = false;
            $('input[name="condiment_name[]"]').each(function(index) {
                var name = $(this).val();
                var quantity = $('input[name="condiment_quantity[]"]').eq(index).val();

                if (name && quantity && quantity > 0) {
                    hasCondiments = true;
                    return false; // 跳出循环
                }
            });

            if (!hasSelectedIngredients && !hasCondiments) {
                e.preventDefault();
                alert('请至少选择一个食材或添加一个调味品');
                return false;
            }

            return true;
        });
    });
</script>
{% endblock %}