{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">审核批次</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('product_batch.index') }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <h5><i class="icon fas fa-info"></i> 批次信息</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>批次名称：</strong>{{ batch.name }}</p>
                                        <p><strong>分类：</strong>{{ batch.category.name if batch.category else '' }}</p>
                                        <p><strong>供应商：</strong>{{ batch.supplier.name if batch.supplier else '' }}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>状态：</strong>
                                            {% if batch.status == 'pending' %}
                                            <span class="badge badge-warning">待审核</span>
                                            {% elif batch.status == 'approved' %}
                                            <span class="badge badge-success">已审核</span>
                                            {% elif batch.status == 'shelved' %}
                                            <span class="badge badge-info">已上架</span>
                                            {% elif batch.status == 'rejected' %}
                                            <span class="badge badge-danger">已拒绝</span>
                                            {% endif %}
                                        </p>
                                        <p><strong>创建时间：</strong>{% if batch.created_at is defined and batch.created_at is not string and batch.created_at is not none %}{{ batch.created_at.strftime('%Y-%m-%d %H:%M:%S') }}{% else %}{{ batch.created_at }}{% endif %}</p>
                                        <p><strong>创建者：</strong>{{ batch.creator.real_name or batch.creator.username if batch.creator else '' }}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">产品列表 ({{ products|length }})</h3>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr>
                                                    <th>产品名称</th>
                                                    <th>价格(元)</th>
                                                    <th>规格</th>
                                                    <th>质量认证</th>
                                                    <th>状态</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for product in products %}
                                                <tr>
                                                    <td>{{ product.product_name or product.ingredient.name if product.ingredient else '' }}</td>
                                                    <td>{{ product.price }}</td>
                                                    <td>{{ product.specification }}</td>
                                                    <td>{{ product.quality_cert }}</td>
                                                    <td>
                                                        {% if product.shelf_status == 0 %}
                                                        <span class="badge badge-warning">待审核</span>
                                                        {% elif product.shelf_status == 1 %}
                                                            {% if product.is_available == 1 %}
                                                            <span class="badge badge-info">已上架</span>
                                                            {% else %}
                                                            <span class="badge badge-success">已审核</span>
                                                            {% endif %}
                                                        {% elif product.shelf_status == 2 %}
                                                        <span class="badge badge-danger">已拒绝</span>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                {% else %}
                                                <tr>
                                                    <td colspan="5" class="text-center">暂无产品</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <form method="post" class="mt-3">
                                {{ form.csrf_token }}
                                {{ form.batch_id }}

                                <div class="mb-3">
                                    <div class="custom-control custom-checkbox">
                                        {{ form.approve_all(class="custom-control-input") }}
                                        <label class="custom-control-label" for="approve_all">
                                            全部审核通过
                                        </label>
                                    </div>
                                </div>

                                <div id="rejectReasonGroup" class="mb-3" style="display: none;">
                                    {{ form.reject_reason.label }}
                                    {{ form.reject_reason(class="form-control", rows=3, placeholder="请输入拒绝原因") }}
                                    {% if form.reject_reason.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.reject_reason.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="alert alert-warning">
                                    <h5><i class="icon fas fa-exclamation-triangle"></i> 审核提示</h5>
                                    <p>请确认以上产品信息无误，提交后将更新 {{ products|length }} 个产品的审核状态。</p>
                                </div>

                                <div class="mb-3 text-center">
                                    {{ form.submit(class="btn btn-primary") }}
                                    <a href="{{ url_for('product_batch.view', id=batch.id) }}" class="btn btn-default">返回详情</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    // 根据审核选项显示/隐藏拒绝原因输入框
    $('#approve_all').change(function() {
        if ($(this).prop('checked')) {
            $('#rejectReasonGroup').hide();
        } else {
            $('#rejectReasonGroup').show();
        }
    });

    // 初始化
    $('#approve_all').trigger('change');
});
</script>
{% endblock %}
