{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题和操作按钮 -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h3 class="mb-0">{{ title }}</h3>
        <div>
            <a href="{{ url_for('consumption_plan.select_weekly_menu') }}" class="btn btn-outline-info me-2">
                <i class="fas fa-calendar-alt"></i> 选择其他周菜单
            </a>
            <a href="{{ url_for('consumption_plan.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> 返回消耗计划
            </a>
        </div>
    </div>

    <!-- 周菜单信息 -->
    <div class="card mb-3">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-calendar-week"></i> 周菜单信息
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <strong>学校：</strong>{{ weekly_menu.area.name }}
                </div>
                <div class="col-md-3">
                    <strong>周期：</strong>{{ weekly_menu.week_start.strftime('%Y-%m-%d') }} 至 {{ weekly_menu.week_end.strftime('%Y-%m-%d') }}
                </div>
                <div class="col-md-3">
                    <strong>状态：</strong>
                    <span class="badge badge-success">{{ weekly_menu.status }}</span>
                </div>
                <div class="col-md-3">
                    <strong>仓库：</strong>{{ warehouse.name }}
                </div>
            </div>
        </div>
    </div>

    <!-- 创建消耗计划表单 -->
    <form method="post" action="{{ url_for('consumption_plan.process_weekly_creation') }}">
        {{ csrf_token() }}
        <input type="hidden" name="weekly_menu_id" value="{{ weekly_menu.id }}">
        <input type="hidden" name="warehouse_id" value="{{ warehouse.id }}">

        <!-- 周菜单详情 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-utensils"></i> 选择要创建消耗计划的日期和餐次
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="thead-light">
                            <tr>
                                <th style="width: 100px;">日期</th>
                                <th style="width: 80px;">星期</th>
                                <th>早餐</th>
                                <th>午餐</th>
                                <th>晚餐</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for date_str, day_data in week_data.items() %}
                            <tr>
                                <td>
                                    <strong>{{ date_str[5:] }}</strong>
                                </td>
                                <td>
                                    <span class="badge badge-secondary">{{ day_data.weekday }}</span>
                                </td>

                                <!-- 早餐 -->
                                <td>
                                    {% if day_data.meals['早餐'] %}
                                    <div class="meal-container">
                                        {% set meal_key = date_str + '_早餐' %}

                                        <!-- 已存在的消耗计划提示 -->
                                        {% if meal_key in existing_plans_map %}
                                        <div class="existing-plan-info">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-info">
                                                    <i class="fas fa-info-circle"></i>
                                                    已有消耗计划
                                                </small>
                                                <span class="badge badge-info badge-sm">{{ existing_plans_map[meal_key][0].status }}</span>
                                            </div>
                                            <a href="{{ url_for('consumption_plan.view', id=existing_plans_map[meal_key][0].id) }}"
                                               class="btn btn-outline-info btn-xs mt-1" target="_blank">
                                                <i class="fas fa-eye"></i> 查看
                                            </a>
                                        </div>
                                        <hr class="my-2">
                                        {% endif %}

                                        <div class="meal-header">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox"
                                                       name="selected_meals"
                                                       value="{{ date_str }}_早餐"
                                                       id="meal_{{ date_str }}_breakfast">
                                                <label class="form-check-label" for="meal_{{ date_str }}_breakfast">
                                                    <strong>创建早餐消耗计划</strong>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="recipes-grid">
                                            {% for recipe in day_data.meals['早餐'] %}
                                            <div class="recipe-item">
                                                <i class="fas fa-utensils text-muted"></i> {{ recipe.name }}
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                    {% else %}
                                    <div class="no-menu-container">
                                        <span class="text-muted">
                                            <i class="fas fa-minus-circle"></i> 无菜单安排
                                        </span>
                                    </div>
                                    {% endif %}
                                </td>

                                <!-- 午餐 -->
                                <td>
                                    {% if day_data.meals['午餐'] %}
                                    <div class="meal-container">
                                        {% set meal_key = date_str + '_午餐' %}

                                        <!-- 已存在的消耗计划提示 -->
                                        {% if meal_key in existing_plans_map %}
                                        <div class="existing-plan-info">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-info">
                                                    <i class="fas fa-info-circle"></i>
                                                    已有消耗计划
                                                </small>
                                                <span class="badge badge-info badge-sm">{{ existing_plans_map[meal_key][0].status }}</span>
                                            </div>
                                            <a href="{{ url_for('consumption_plan.view', id=existing_plans_map[meal_key][0].id) }}"
                                               class="btn btn-outline-info btn-xs mt-1" target="_blank">
                                                <i class="fas fa-eye"></i> 查看
                                            </a>
                                        </div>
                                        <hr class="my-2">
                                        {% endif %}

                                        <div class="meal-header">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox"
                                                       name="selected_meals"
                                                       value="{{ date_str }}_午餐"
                                                       id="meal_{{ date_str }}_lunch">
                                                <label class="form-check-label" for="meal_{{ date_str }}_lunch">
                                                    <strong>创建午餐消耗计划</strong>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="recipes-grid">
                                            {% for recipe in day_data.meals['午餐'] %}
                                            <div class="recipe-item">
                                                <i class="fas fa-utensils text-muted"></i> {{ recipe.name }}
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                    {% else %}
                                    <div class="no-menu-container">
                                        <span class="text-muted">
                                            <i class="fas fa-minus-circle"></i> 无菜单安排
                                        </span>
                                    </div>
                                    {% endif %}
                                </td>

                                <!-- 晚餐 -->
                                <td>
                                    {% if day_data.meals['晚餐'] %}
                                    <div class="meal-container">
                                        {% set meal_key = date_str + '_晚餐' %}

                                        <!-- 已存在的消耗计划提示 -->
                                        {% if meal_key in existing_plans_map %}
                                        <div class="existing-plan-info">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-info">
                                                    <i class="fas fa-info-circle"></i>
                                                    已有消耗计划
                                                </small>
                                                <span class="badge badge-info badge-sm">{{ existing_plans_map[meal_key][0].status }}</span>
                                            </div>
                                            <a href="{{ url_for('consumption_plan.view', id=existing_plans_map[meal_key][0].id) }}"
                                               class="btn btn-outline-info btn-xs mt-1" target="_blank">
                                                <i class="fas fa-eye"></i> 查看
                                            </a>
                                        </div>
                                        <hr class="my-2">
                                        {% endif %}

                                        <div class="meal-header">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox"
                                                       name="selected_meals"
                                                       value="{{ date_str }}_晚餐"
                                                       id="meal_{{ date_str }}_dinner">
                                                <label class="form-check-label" for="meal_{{ date_str }}_dinner">
                                                    <strong>创建晚餐消耗计划</strong>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="recipes-grid">
                                            {% for recipe in day_data.meals['晚餐'] %}
                                            <div class="recipe-item">
                                                <i class="fas fa-utensils text-muted"></i> {{ recipe.name }}
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                    {% else %}
                                    <div class="no-menu-container">
                                        <span class="text-muted">
                                            <i class="fas fa-minus-circle"></i> 无菜单安排
                                        </span>
                                    </div>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- 批量选择按钮 -->
                <div class="mt-3">
                    <button type="button" class="btn btn-outline-primary btn-sm" data-onclick="selectAll()">
                        <i class="fas fa-check-square"></i> 全选
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm ms-2" data-onclick="selectNone()">
                        <i class="fas fa-square"></i> 全不选
                    </button>
                    <button type="button" class="btn btn-outline-success btn-sm ms-2" data-onclick="analyzeSelected()">
                        <i class="fas fa-search"></i> 分析选中餐次
                    </button>
                </div>

                <!-- 分析结果显示区域 -->
                <div id="analysis-result" class="mt-3" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-chart-line"></i> 选中餐次分析结果
                            </h6>
                        </div>
                        <div class="card-body" id="analysis-content">
                            <!-- 动态内容将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 提交按钮 -->
        <div class="mt-3 text-center">
            <button type="submit" class="btn btn-primary btn-lg">
                <i class="fas fa-plus"></i> 创建选中的消耗计划
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
/* 餐次容器 */
.meal-container {
    width: 100%;
}

/* 已存在消耗计划信息 */
.existing-plan-info {
    background-color: #f8f9fa;
    padding: 6px 8px;
    border-radius: 4px;
    border-start: 3px solid #17a2b8;
    margin-bottom: 8px;
}

/* 相似度警告 */
.similarity-warning {
    background-color: #fff3cd;
    padding: 6px 8px;
    border-radius: 4px;
    border-start: 3px solid #ffc107;
    margin-bottom: 8px;
}

.similar-plan-item {
    margin-top: 4px;
    padding-left: 8px;
}

/* 无菜单容器 */
.no-menu-container {
    padding: 20px 8px;
    text-align: center;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px dashed #dee2e6;
}

.no-menu-container .text-muted {
    font-size: 15px;
    color: #6c757d !important;
}

.btn-xs {
    padding: 2px 6px;
    font-size: 11px;
    line-height: 1.2;
}

/* 餐次标题区域 */
.meal-header {
    margin-bottom: 8px;
}

.form-check {
    background-color: #f8f9fa;
    padding: 6px 8px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    margin-bottom: 0;
}

.form-check-label {
    font-weight: 500;
    color: #495057;
    font-size: 15px;
}

/* 菜谱网格布局 - 两列显示 */
.recipes-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6px 10px;
    margin-top: 10px;
}

.recipe-item {
    padding: 4px 0;
    font-size: 15px;
    color: #495057;
    line-height: 1.4;
    word-break: break-word;
}

.recipe-item i {
    margin-right: 4px;
    font-size: 12px;
    color: #6c757d;
}

/* 表格样式 */
.table td {
    vertical-align: top;
    padding: 10px 6px;
    min-width: 180px;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    text-align: center;
    padding: 8px 6px;
}

/* 让表格更紧凑 */
.table-responsive {
    font-size: 15px;
}

/* 优化复选框样式 */
.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

/* 让日期列更突出 */
.table td:first-child {
    font-weight: 600;
    color: #495057;
    text-align: center;
    min-width: 80px;
}

/* 星期列样式 */
.table td:nth-child(2) {
    text-align: center;
    min-width: 60px;
}

/* 响应式调整 */
@d-flex (max-width: 768px) {
    .recipes-grid {
        grid-template-columns: 1fr;
        gap: 4px;
    }

    .table td {
        min-width: 120px;
        padding: 8px 4px;
    }

    .recipe-item {
        font-size: 15px;
    }

    .form-check-label {
        font-size: 15px;
    }
}
</style>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
function selectAll() {
    document.querySelectorAll('input[name="selected_meals"]').forEach(function(checkbox) {
        checkbox.checked = true;
    });
}

function selectNone() {
    document.querySelectorAll('input[name="selected_meals"]').forEach(function(checkbox) {
        checkbox.checked = false;
    });
}

function analyzeSelected() {
    // 获取选中的餐次
    const selectedMeals = [];
    document.querySelectorAll('input[name="selected_meals"]:checked').forEach(function(checkbox) {
        selectedMeals.push(checkbox.value);
    });

    if (selectedMeals.length === 0) {
        alert('请先选择要分析的餐次');
        return;
    }

    // 显示加载状态
    const analysisResult = document.getElementById('analysis-result');
    const analysisContent = document.getElementById('analysis-content');

    analysisResult.style.display = 'block';
    analysisContent.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> 正在分析选中餐次...</div>';

    // 发送AJAX请求
    fetch('/consumption-plan/analyze-selected-meals', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            weekly_menu_id: {{ weekly_menu.id }},
            warehouse_id: {{ warehouse.id }},
            selected_meals: selectedMeals
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayAnalysisResult(data.data);
        } else {
            analysisContent.innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> ' + data.message + '</div>';
        }
    })
    .catch(error => {
        console.error('分析失败:', error);
        analysisContent.innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> 分析失败，请重试</div>';
    });
}

function displayAnalysisResult(data) {
    const analysisContent = document.getElementById('analysis-content');
    let html = '';

    // 总体统计
    html += '<div class="row mb-3">';
    html += '<div class="col-md-3"><div class="text-center"><h5 class="text-primary">' + Object.keys(data.meal_analysis).length + '</h5><small>选中餐次</small></div></div>';
    html += '<div class="col-md-3"><div class="text-center"><h5 class="text-info">' + data.total_ingredients + '</h5><small>所需食材</small></div></div>';
    html += '<div class="col-md-3"><div class="text-center"><h5 class="' + (data.insufficient_count > 0 ? 'text-danger' : 'text-success') + '">' + data.insufficient_count + '</h5><small>库存不足</small></div></div>';
    html += '<div class="col-md-3"><div class="text-center"><h5 class="' + (data.inventory_sufficient ? 'text-success' : 'text-warning') + '"><i class="fas fa-' + (data.inventory_sufficient ? 'check' : 'exclamation-triangle') + '"></i></h5><small>' + (data.inventory_sufficient ? '库存充足' : '需要采购') + '</small></div></div>';
    html += '</div>';

    // 库存不足提醒
    if (data.insufficient_count > 0) {
        html += '<div class="alert alert-warning">';
        html += '<h6><i class="fas fa-exclamation-triangle"></i> 库存不足食材 (' + data.insufficient_count + '种)</h6>';
        html += '<div class="row">';
        data.insufficient_ingredients.forEach(function(item, index) {
            if (index < 6) { // 只显示前6个
                html += '<div class="col-md-4 mb-2">';
                html += '<small><strong>' + item.name + '</strong>: 缺 ' + item.shortage + item.unit + '</small>';
                html += '</div>';
            }
        });
        if (data.insufficient_ingredients.length > 6) {
            html += '<div class="col-12"><small class="text-muted">还有 ' + (data.insufficient_ingredients.length - 6) + ' 种食材库存不足...</small></div>';
        }
        html += '</div>';
        html += '</div>';
    }

    // 整体食材分析
    html += '<div class="mt-3">';
    html += '<h6>整体食材分析（仅主材，不含调味料）</h6>';

    // 统计所有餐次的食材分类
    const allCategories = {};
    const allIngredients = new Set();

    Object.keys(data.meal_analysis).forEach(function(mealKey) {
        const meal = data.meal_analysis[mealKey];
        if (meal.has_recipes && meal.ingredient_by_category) {
            Object.keys(meal.ingredient_by_category).forEach(function(category) {
                if (!allCategories[category]) {
                    allCategories[category] = new Set();
                }
                meal.ingredient_by_category[category].forEach(function(ing) {
                    allCategories[category].add(ing.name);
                    allIngredients.add(ing.name);
                });
            });
        }
    });

    if (Object.keys(allCategories).length > 0) {
        html += '<div class="row mb-3">';
        html += '<div class="col-12">';
        html += '<div class="alert alert-info">';
        html += '<h6 class="mb-2"><i class="fas fa-chart-pie"></i> 食材分类统计</h6>';
        html += '<div class="row">';

        Object.keys(allCategories).forEach(function(category) {
            const count = allCategories[category].size;
            html += '<div class="col-md-3 mb-2">';
            html += '<span class="badge badge-primary me-1">' + category + '</span>';
            html += '<small class="text-muted">' + count + '种</small>';
            html += '</div>';
        });

        html += '</div>';
        html += '<div class="mt-2">';
        html += '<small class="text-muted"><strong>总计:</strong> ' + allIngredients.size + '种不同的主材食材</small>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
    }
    html += '</div>';

    // 餐次详情
    html += '<div class="mt-3">';
    html += '<h6>餐次详情</h6>';
    html += '<div class="row">';

    Object.keys(data.meal_analysis).forEach(function(mealKey) {
        const meal = data.meal_analysis[mealKey];
        const [dateStr, mealType] = mealKey.split('_');

        html += '<div class="col-md-6 mb-3">';
        html += '<div class="card card-sm">';
        html += '<div class="card-header py-2">';
        html += '<h6 class="mb-0">' + dateStr.substring(5) + ' ' + mealType + '</h6>';
        html += '</div>';
        html += '<div class="card-body py-2">';

        if (meal.has_recipes) {
            // 基本统计信息
            html += '<div class="mb-2">';
            html += '<small class="text-muted"><strong>食谱:</strong> ' + meal.recipes_count + '个</small><br>';
            html += '<small class="text-muted"><strong>主材:</strong> ' + meal.main_ingredients_count + '种（不含调味料）</small><br>';
            html += '<small class="text-muted"><strong>分类:</strong> ' + meal.category_stats.total_categories + '类</small>';
            html += '</div>';

            // 食材分类详情
            if (meal.ingredient_by_category && Object.keys(meal.ingredient_by_category).length > 0) {
                html += '<div class="ingredient-categories mb-2">';
                html += '<small class="text-muted"><strong>食材分类详情:</strong></small>';
                html += '<div class="mt-1">';

                Object.keys(meal.ingredient_by_category).forEach(function(category) {
                    const ingredients = meal.ingredient_by_category[category];
                    const uniqueIngredients = [];
                    const seenIngredients = new Set();

                    // 去重，只显示唯一的食材名称
                    ingredients.forEach(function(ing) {
                        if (!seenIngredients.has(ing.name)) {
                            seenIngredients.add(ing.name);
                            uniqueIngredients.push(ing);
                        }
                    });

                    html += '<div class="category-group">';
                    html += '<span class="badge badge-light badge-sm me-1">' + category + ' (' + uniqueIngredients.length + ')</span>';
                    html += '<small class="text-muted">';
                    uniqueIngredients.slice(0, 3).forEach(function(ing, index) {
                        html += ing.name;
                        if (index < Math.min(2, uniqueIngredients.length - 1)) html += ', ';
                    });
                    if (uniqueIngredients.length > 3) {
                        html += '等' + uniqueIngredients.length + '种';
                    }
                    html += '</small><br>';
                    html += '</div>';
                });
                html += '</div>';
                html += '</div>';
            }

            // 智能建议
            if (meal.smart_suggestions) {
                const suggestion = meal.smart_suggestions;
                let badgeClass = 'badge-secondary';
                let iconClass = 'fas fa-info-circle';

                if (suggestion.action === 'skip') {
                    badgeClass = 'badge-danger';
                    iconClass = 'fas fa-times-circle';
                } else if (suggestion.action === 'review') {
                    badgeClass = 'badge-warning';
                    iconClass = 'fas fa-exclamation-triangle';
                } else if (suggestion.action === 'create_new') {
                    badgeClass = 'badge-success';
                    iconClass = 'fas fa-plus-circle';
                }

                html += '<div class="mt-2">';
                html += '<span class="badge ' + badgeClass + ' badge-sm"><i class="' + iconClass + '"></i> ' + suggestion.action.replace('_', ' ').toUpperCase() + '</span>';
                html += '<div class="mt-1"><small class="text-muted">' + suggestion.message + '</small></div>';
                html += '</div>';
            }

            // 相似度分析
            if (meal.similarity_analysis.has_similar) {
                html += '<div class="mt-2">';
                html += '<span class="badge badge-warning badge-sm">相似度 ' + meal.similarity_analysis.max_similarity_percent + '%</span>';
                html += '</div>';
            }

            // 已存在的消耗计划
            if (meal.existing_plans && meal.existing_plans.length > 0) {
                html += '<div class="mt-2">';
                html += '<span class="badge badge-info badge-sm">已有 ' + meal.existing_plans.length + ' 个计划</span>';
                html += '</div>';
            }
        } else {
            html += '<small class="text-muted">' + meal.message + '</small>';
        }

        html += '</div>';
        html += '</div>';
        html += '</div>';
    });

    html += '</div>';
    html += '</div>';

    analysisContent.innerHTML = html;
}
</script>
{% endblock %}

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>