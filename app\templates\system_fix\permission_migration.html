{% extends 'base.html' %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2>{{ title }}</h2>
        <p class="text-muted">此工具用于自动迁移角色权限，处理模块变化和合并</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('system_fix.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回系统修复
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">模块变化说明</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <p><i class="fas fa-info-circle"></i> 系统模块变化：</p>
                    <ul>
                        <li><strong>菜单计划管理</strong> 已被 <strong>周菜单管理</strong> 模块替代</li>
                        <li><strong>留样管理</strong> 已与 <strong>食材溯源与留样</strong> 模块合并</li>
                        <li>新增 <strong>食堂日常管理</strong> 模块，用于管理食堂日常运营</li>
                    </ul>
                </div>
                
                <p>此工具可以自动迁移角色权限，确保角色在模块变化后仍然拥有适当的权限。</p>
                <p>迁移操作包括：</p>
                <ol>
                    <li>将拥有"菜单计划管理"权限的角色添加相应的"周菜单管理"权限</li>
                    <li>将拥有"留样管理"权限的角色添加相应的"食材溯源与留样"权限</li>
                    <li>为食堂管理员角色添加"食堂日常管理"的所有权限</li>
                </ol>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header bg-warning">
                        <h5 class="mb-0">菜单计划 → 周菜单</h5>
                    </div>
                    <div class="card-body">
                        <p>需要迁移的角色数量：<strong>{{ menu_plan_roles|length }}</strong></p>
                        {% if menu_plan_roles %}
                        <ul>
                            {% for role in menu_plan_roles %}
                            <li>{{ role.name }}</li>
                            {% endfor %}
                        </ul>
                        <a href="{{ url_for('permission_migration.migrate_menu_plan') }}" class="btn btn-warning btn-block">
                            <i class="fas fa-sync-alt"></i> 迁移菜单计划权限
                        </a>
                        {% else %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> 所有角色已完成迁移
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header bg-info">
                        <h5 class="mb-0">留样 → 食材溯源与留样</h5>
                    </div>
                    <div class="card-body">
                        <p>需要迁移的角色数量：<strong>{{ sample_roles|length }}</strong></p>
                        {% if sample_roles %}
                        <ul>
                            {% for role in sample_roles %}
                            <li>{{ role.name }}</li>
                            {% endfor %}
                        </ul>
                        <a href="{{ url_for('permission_migration.migrate_sample') }}" class="btn btn-info btn-block">
                            <i class="fas fa-sync-alt"></i> 迁移留样权限
                        </a>
                        {% else %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> 所有角色已完成迁移
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header bg-success">
                        <h5 class="mb-0">添加食堂日常管理权限</h5>
                    </div>
                    <div class="card-body">
                        <p>为所有食堂管理员角色添加食堂日常管理权限</p>
                        <a href="{{ url_for('permission_migration.add_daily_management') }}" class="btn btn-success btn-block">
                            <i class="fas fa-plus-circle"></i> 添加食堂日常管理权限
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0">所有角色</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>角色名称</th>
                                <th>菜单计划</th>
                                <th>周菜单</th>
                                <th>留样</th>
                                <th>食材溯源与留样</th>
                                <th>食堂日常管理</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for role in roles %}
                            {% set permissions = role.permissions|fromjson %}
                            <tr>
                                <td>{{ role.id }}</td>
                                <td>{{ role.name }}</td>
                                <td>
                                    {% if 'menu_plan' in permissions %}
                                    <span class="badge badge-success"><i class="fas fa-check"></i></span>
                                    {% else %}
                                    <span class="badge badge-secondary"><i class="fas fa-times"></i></span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if 'weekly_menu' in permissions %}
                                    <span class="badge badge-success"><i class="fas fa-check"></i></span>
                                    {% else %}
                                    <span class="badge badge-secondary"><i class="fas fa-times"></i></span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if 'sample' in permissions %}
                                    <span class="badge badge-success"><i class="fas fa-check"></i></span>
                                    {% else %}
                                    <span class="badge badge-secondary"><i class="fas fa-times"></i></span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if 'traceability' in permissions %}
                                    <span class="badge badge-success"><i class="fas fa-check"></i></span>
                                    {% else %}
                                    <span class="badge badge-secondary"><i class="fas fa-times"></i></span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if 'daily_management' in permissions %}
                                    <span class="badge badge-success"><i class="fas fa-check"></i></span>
                                    {% else %}
                                    <span class="badge badge-secondary"><i class="fas fa-times"></i></span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('system.edit_role_permissions', id=role.id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i> 编辑权限
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
