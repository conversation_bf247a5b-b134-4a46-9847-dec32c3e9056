{% extends 'base.html' %}

{% block title %}周菜单计划 - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('static', filename='css/save-indicator.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/weekly_menu_modal.css') }}">
<style nonce="{{ csp_nonce }}">
  /* 页面特定样式 */
  .table th, .table td {
    vertical-align: middle;
    text-align: center;
  }

  .menu-input {
    cursor: pointer;
    background-color: #fff;
    transition: all 0.3s;
  }

  .menu-input:hover {
    background-color: #f8f9fa;
  }

  .menu-input.selected {
    background-color: #e8f4e8;
    border-color: #28a745;
  }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row mb-4">
    <div class="col-md-8">
      <h2>{{ area.name }}周菜单计划</h2>
      <p class="text-muted">{{ week_start }} 至 {{ week_end }}</p>
    </div>
    <div class="col-md-4 text-end">
      <a href="{{ url_for('weekly_menu.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> 返回列表
      </a>
    </div>
  </div>

  <div class="row">
    <div class="col-md-12">
      <div class="card">
        <div class="card-body">
          <form id="menuForm" method="post">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            <input type="hidden" name="menu_data" id="menuData" value="">
            <input type="hidden" name="area_id" id="area_id" value="{{ area.id }}">

            <table class="table table-bordered">
              <thead>
                <tr>
                  <th class="w-15">日期</th>
                  <th>早餐</th>
                  <th>午餐</th>
                  <th>晚餐</th>
                </tr>
              </thead>
              <tbody>
                {% for date_str, day_data in week_dates.items() %}
                <tr>
                  <td>
                    <div class="fw-bold">{{ day_data.weekday }}</div>
                    <div>{{ date_str }}</div>
                  </td>
                  {% for meal_type in ['早餐', '午餐', '晚餐'] %}
                  <td>
                    <input type="text"
                           class="form-control menu-input"
                           data-date="{{ date_str }}"
                           data-meal="{{ meal_type }}"
                           placeholder="点击选择菜谱"
                           readonly
                           value="{{ menu_data[date_str][meal_type]|join(', ') if menu_data.get(date_str, {}).get(meal_type) }}">
                  </td>
                  {% endfor %}
                </tr>
                {% endfor %}
              </tbody>
            </table>

            <div class="text-center mt-4">
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> 保存菜单计划
              </button>
              <!-- 不再需要清除缓存按钮 -->
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 食谱选择模态框 -->
<div class="modal" id="recipeModal" tabindex="-1" aria-labelledby="recipeModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="recipeModalLabel">选择食谱 <span id="modal-date"></span></h5>
        <button type="button" class="btn-close" data-onclick="closeModal()">×</button>
      </div>
      <div class="modal-body">
        <div class="row">
          <!-- 左侧：食谱分类 -->
          <div class="col-md-4">
            <div class="list-group mb-4">
              <a href="#" class="list-group-item list-group-item-action active recipe-category" data-category="all">
                全部食谱
              </a>
              {% for category, recipes in recipes_by_category.items() %}
              <a href="#" class="list-group-item list-group-item-action recipe-category" data-category="{{ category }}">
                {{ category }}
              </a>
              {% endfor %}
            </div>

            <!-- 自定义食谱 -->
            <div class="card">
              <div class="card-header">自定义食谱</div>
              <div class="card-body">
                <div id="customDishContainer">
                  <input type="text"
                         id="customDishInput"
                         class="form-control mb-2"
                         placeholder="输入新食谱">
                  <button class="btn btn-sm btn-success" id="addCustomDishBtn">
                    添加
                  </button>
                </div>
                <div id="selectedDishes" class="mt-3"></div>
              </div>
            </div>
          </div>

          <!-- 右侧：食谱列表 -->
          <div class="col-md-8">
            <div class="row" id="recipeList">
              {% for category, recipes in recipes_by_category.items() %}
                {% for recipe in recipes %}
                <div class="col-md-4 mb-3">
                  <div class="recipe-card" data-id="{{ recipe.id }}" data-category="{{ category }}" data-onclick="selectDish("{{ recipe.id }}', '{{ recipe.name }}')">
                    <div class="dish-name">{{ recipe.name }}</div>
                    <div class="text-muted small">{{ category }}</div>
                  </div>
                </div>
                {% endfor %}
              {% endfor %}
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" id="cancelSelectionBtn">取消</button>
        <button type="button" class="btn btn-primary" id="saveSelectionBtn">保存</button>
      </div>
    </div>
  </div>
</div>
<div class="modal-backdrop"></div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
// 初始化菜单数据
window.menuData = {{ menu_data|tojson|safe }};
// 设置区域ID
window.realTimeSave = true; // 启用实时保存
</script>
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/weekly_menu_modal.js') }}"></script>
{% endblock %}

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>