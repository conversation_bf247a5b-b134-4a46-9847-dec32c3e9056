{% extends 'base.html' %}

{% block title %}{{ title }}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='vendor/datatables/css/dataTables.bootstrap5.min.css') }}">
<style nonce="{{ csp_nonce }}">
    .action-buttons .btn {
        margin-right: 5px;
    }

    .rating-stars {
        color: #f6c23e;
    }

    .meal-badge {
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .meal-morning {
        background-color: #4e73df;
        color: white;
    }

    .meal-noon {
        background-color: #1cc88a;
        color: white;
    }

    .meal-evening {
        background-color: #f6c23e;
        color: white;
    }

    .table-responsive {
        overflow-x: auto;
    }

    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter {
        margin-bottom: 15px;
    }

    .photo-item {
        position: relative;
        width: 200px;
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 0.15rem 0.5rem rgba(58, 59, 69, 0.1);
    }

    .photo-item img {
        width: 100%;
        height: 150px;
        object-fit: cover;
    }

    .photo-info {
        padding: 0.5rem;
        background-color: #f8f9fc;
    }

    .upload-time {
        font-size: 0.8rem;
        color: #858796;
    }

    /* 新增的评分和照片样式 */
    .rating-container {
        display: flex;
        gap: 2px;
    }

    .rating-star {
        font-size: 1.2rem;
        color: #ddd;
        cursor: pointer;
        transition: color 0.2s ease;
        user-select: none;
    }

    .rating-star:hover,
    .rating-star.active {
        color: #ffc107;
    }

    .rating-star:hover ~ .rating-star {
        color: #ddd;
    }

    .rating-container:hover .rating-star {
        color: #ddd;
    }

    .rating-container:hover .rating-star:hover,
    .rating-container:hover .rating-star:hover ~ .rating-star {
        color: #ffc107;
    }

    .photo-thumbnail {
        width: 80px;
        height: 80px;
        object-fit: cover;
        border-radius: 5px;
        cursor: pointer;
        transition: transform 0.2s ease;
    }

    .photo-thumbnail:hover {
        transform: scale(1.1);
    }
</style>

{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 导入导航宏 -->
    {% from 'daily_management/components/navigation.html' import daily_management_header %}

    <!-- 显示导航和学校信息 -->
    {{ daily_management_header(title, school, log, 'inspections') }}

    <!-- 功能按钮 -->
    <div class="mb-4">
        <a href="{{ url_for('daily_management.add_inspection_photo', log_id=log.id, type='morning') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> 添加早晨检查
        </a>
        <a href="{{ url_for('daily_management.add_inspection_photo', log_id=log.id, type='noon') }}" class="btn btn-success">
            <i class="fas fa-plus me-1"></i> 添加中午检查
        </a>
        <a href="{{ url_for('daily_management.add_inspection_photo', log_id=log.id, type='evening') }}" class="btn btn-warning">
            <i class="fas fa-plus me-1"></i> 添加晚上检查
        </a>
        <a href="{{ url_for('daily_management.generate_photo_upload_qrcode', log_id=log.id) }}" class="btn btn-info">
            <i class="fas fa-qrcode me-1"></i> 生成二维码
        </a>
        <a href="{{ url_for('daily_management.rate_inspection_photos', log_id=log.id) }}" class="btn btn-warning">
            <i class="fas fa-star me-1"></i> 评价照片
        </a>
        <a href="{{ url_for('daily_management.print_inspection_photos', log_id=log.id) }}" class="btn btn-success">
            <i class="fas fa-print me-1"></i> 打印检查记录
        </a>
        <a href="{{ url_for('daily_management.edit_log', date_str=log.log_date) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i> 返回日志
        </a>
    </div>

    <!-- 检查记录列表 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 fw-bold text-primary">检查记录列表</h6>
        </div>
        <div class="card-body">
            <ul class="nav nav-tabs mb-4" id="inspectionTabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" id="morning-tab" data-bs-toggle="tab" href="#morning" role="tab" aria-controls="morning" aria-selected="true">
                        <i class="fas fa-sun me-1"></i> 早晨检查
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="noon-tab" data-bs-toggle="tab" href="#noon" role="tab" aria-controls="noon" aria-selected="false">
                        <i class="fas fa-cloud-sun me-1"></i> 中午检查
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="evening-tab" data-bs-toggle="tab" href="#evening" role="tab" aria-controls="evening" aria-selected="false">
                        <i class="fas fa-moon me-1"></i> 晚上检查
                    </a>
                </li>
            </ul>

            <div class="tab-content" id="inspectionTabsContent">
                <!-- 早晨检查 -->
                <div class="tab-pane fade show active" id="morning" role="tabpanel" aria-labelledby="morning-tab">
                    {% if morning_photos %}
                    <div class="table-responsive">
                        <table class="table table-bordered" id="morningTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>照片</th>
                                    <th>上传时间</th>
                                    <th>评分</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for photo in morning_photos %}
                                <tr>
                                    <td>
                                        <img src="{{ photo.file_path }}" alt="早晨检查照片" class="photo-thumbnail" data-onclick="viewPhoto("{{ photo.file_path }}')">
                                    </td>
                                    <td>{{ photo.upload_time|format_datetime }}</td>
                                    <td>
                                        <div class="rating-container" data-photo-id="{{ photo.id }}">
                                            {% for i in range(1, 6) %}
                                            <span class="rating-star {% if photo.rating and i <= photo.rating %}active{% endif %}"
                                                  data-rating="{{ i }}"
                                                  data-onclick="updateRating({{ photo.id }}, {{ i }})">★</span>
                                            {% endfor %}
                                        </div>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('daily_management.view_inspection_photo', photo_id=photo.id) }}" class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i> 查看
                                        </a>
                                        <a href="{{ url_for('daily_management.edit_inspection_photo', photo_id=photo.id) }}" class="btn btn-primary btn-sm">
                                            <i class="fas fa-edit"></i> 编辑
                                        </a>
                                        <button type="button" class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#deleteModal{{ photo.id }}">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>

                                        <!-- 删除确认模态框 -->
                                        <div class="modal fade" id="deleteModal{{ photo.id }}" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
                                            <div class="modal-dialog" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                                                        <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                                                            <span aria-hidden="true">&times;</span>
                                                        </button>
                                                    </div>
                                                    <div class="modal-body">
                                                        确定要删除这张检查照片吗？此操作不可恢复。
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                                        <form action="{{ url_for('daily_management.delete_inspection_photo', photo_id=photo.id) }}" method="post">
                                                            {{ form.csrf_token }}
                                                            <button type="submit" class="btn btn-danger">确认删除</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <p class="text-muted">暂无早晨检查记录</p>
                        <a href="{{ url_for('daily_management.add_inspection_photo', log_id=log.id, type='morning') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> 添加早晨检查
                        </a>
                    </div>
                    {% endif %}
                </div>

                <!-- 中午检查 -->
                <div class="tab-pane fade" id="noon" role="tabpanel" aria-labelledby="noon-tab">
                    {% if noon_photos %}
                    <div class="table-responsive">
                        <table class="table table-bordered" id="noonTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>照片</th>
                                    <th>上传时间</th>
                                    <th>评分</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for photo in noon_photos %}
                                <tr>
                                    <td>
                                        <img src="{{ photo.file_path }}" alt="中午检查照片" class="photo-thumbnail" data-onclick="viewPhoto("{{ photo.file_path }}')">
                                    </td>
                                    <td>{{ photo.upload_time|format_datetime }}</td>
                                    <td>
                                        <div class="rating-container" data-photo-id="{{ photo.id }}">
                                            {% for i in range(1, 6) %}
                                            <span class="rating-star {% if photo.rating and i <= photo.rating %}active{% endif %}"
                                                  data-rating="{{ i }}"
                                                  data-onclick="updateRating({{ photo.id }}, {{ i }})">★</span>
                                            {% endfor %}
                                        </div>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('daily_management.view_inspection_photo', photo_id=photo.id) }}" class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i> 查看
                                        </a>
                                        <a href="{{ url_for('daily_management.edit_inspection_photo', photo_id=photo.id) }}" class="btn btn-primary btn-sm">
                                            <i class="fas fa-edit"></i> 编辑
                                        </a>
                                        <button type="button" class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#deleteModal{{ photo.id }}">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>

                                        <!-- 删除确认模态框 -->
                                        <div class="modal fade" id="deleteModal{{ photo.id }}" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
                                            <div class="modal-dialog" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                                                        <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                                                            <span aria-hidden="true">&times;</span>
                                                        </button>
                                                    </div>
                                                    <div class="modal-body">
                                                        确定要删除这张检查照片吗？此操作不可恢复。
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                                        <form action="{{ url_for('daily_management.delete_inspection_photo', photo_id=photo.id) }}" method="post">
                                                            {{ form.csrf_token }}
                                                            <button type="submit" class="btn btn-danger">确认删除</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <p class="text-muted">暂无中午检查记录</p>
                        <a href="{{ url_for('daily_management.add_inspection_photo', log_id=log.id, type='noon') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> 添加中午检查
                        </a>
                    </div>
                    {% endif %}
                </div>

                <!-- 晚上检查 -->
                <div class="tab-pane fade" id="evening" role="tabpanel" aria-labelledby="evening-tab">
                    {% if evening_photos %}
                    <div class="table-responsive">
                        <table class="table table-bordered" id="eveningTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>照片</th>
                                    <th>上传时间</th>
                                    <th>评分</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for photo in evening_photos %}
                                <tr>
                                    <td>
                                        <img src="{{ photo.file_path }}" alt="晚上检查照片" class="photo-thumbnail" data-onclick="viewPhoto("{{ photo.file_path }}')">
                                    </td>
                                    <td>{{ photo.upload_time|format_datetime }}</td>
                                    <td>
                                        <div class="rating-container" data-photo-id="{{ photo.id }}">
                                            {% for i in range(1, 6) %}
                                            <span class="rating-star {% if photo.rating and i <= photo.rating %}active{% endif %}"
                                                  data-rating="{{ i }}"
                                                  data-onclick="updateRating({{ photo.id }}, {{ i }})">★</span>
                                            {% endfor %}
                                        </div>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('daily_management.view_inspection_photo', photo_id=photo.id) }}" class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i> 查看
                                        </a>
                                        <a href="{{ url_for('daily_management.edit_inspection_photo', photo_id=photo.id) }}" class="btn btn-primary btn-sm">
                                            <i class="fas fa-edit"></i> 编辑
                                        </a>
                                        <button type="button" class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#deleteModal{{ photo.id }}">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>

                                        <!-- 删除确认模态框 -->
                                        <div class="modal fade" id="deleteModal{{ photo.id }}" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
                                            <div class="modal-dialog" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                                                        <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                                                            <span aria-hidden="true">&times;</span>
                                                        </button>
                                                    </div>
                                                    <div class="modal-body">
                                                        确定要删除这张检查照片吗？此操作不可恢复。
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                                        <form action="{{ url_for('daily_management.delete_inspection_photo', photo_id=photo.id) }}" method="post">
                                                            {{ form.csrf_token }}
                                                            <button type="submit" class="btn btn-danger">确认删除</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <p class="text-muted">暂无晚上检查记录</p>
                        <a href="{{ url_for('daily_management.add_inspection_photo', log_id=log.id, type='evening') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> 添加晚上检查
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}


{% block scripts %}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/datatables/js/jquery.dataTables.min.js') }}"></script>
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/datatables/js/dataTables.bootstrap5.min.js') }}"></script>
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 初始化DataTables
        $('#morningTable').DataTable({
            "language": {
                "url": "{{ url_for('static', filename='vendor/datatables/js/Chinese.json') }}"
            },
            "order": [[1, "desc"]]  // 按上传时间降序排序
        });

        $('#noonTable').DataTable({
            "language": {
                "url": "{{ url_for('static', filename='vendor/datatables/js/Chinese.json') }}"
            },
            "order": [[1, "desc"]]  // 按上传时间降序排序
        });

        $('#eveningTable').DataTable({
            "language": {
                "url": "{{ url_for('static', filename='vendor/datatables/js/Chinese.json') }}"
            },
            "order": [[1, "desc"]]  // 按上传时间降序排序
        });
    });

    // 更新照片评分
    function updateRating(photoId, rating) {
        // 显示加载状态
        const container = document.querySelector(`[data-photo-id="${photoId}"]`);
        const originalContent = container.innerHTML;
        container.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

        // 发送AJAX请求
        fetch('/api/v2/photos/public/rate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                photo_id: photoId,
                rating: rating
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新星级显示
                updateStarDisplay(photoId, rating);

                // 显示成功提示
                showToast('评分更新成功', 'success');
            } else {
                // 恢复原始内容
                container.innerHTML = originalContent;
                showToast(data.error || '评分更新失败', 'error');
            }
        })
        .catch(error => {
            // 恢复原始内容
            container.innerHTML = originalContent;
            showToast('网络错误，请重试', 'error');
            console.error('Error:', error);
        });
    }

    // 更新星级显示
    function updateStarDisplay(photoId, rating) {
        const container = document.querySelector(`[data-photo-id="${photoId}"]`);
        const stars = container.querySelectorAll('.rating-star');

        stars.forEach((star, index) => {
            if (index < rating) {
                star.classList.add('active');
            } else {
                star.classList.remove('active');
            }
        });
    }

    // 显示提示消息
    function showToast(message, type = 'info') {
        // 创建提示元素
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} toast-message`;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 250px;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        toast.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            ${message}
        `;

        // 添加到页面
        document.body.appendChild(toast);

        // 显示动画
        setTimeout(() => {
            toast.style.opacity = '1';
        }, 100);

        // 3秒后自动移除
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    // 查看照片大图
    function viewPhoto(imagePath) {
        // 创建模态框
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">查看照片</h5>
                        <button type="button" class="close" data-bs-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body text-center">
                        <img src="${imagePath}" class="img-fluid" alt="检查照片">
                    </div>
                </div>
            </div>
        `;

        // 添加到页面并显示
        document.body.appendChild(modal);
        $(modal).modal('show');

        // 模态框关闭后移除
        $(modal).on('hidden.bs.modal', function() {
            document.body.removeChild(modal);
        });
    }
</script>

{% endblock %}
