#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bootstrap 5.3.6 迁移超级工具
=========================

这个工具将StudentsCMSSP项目中所有页面的CSS依赖从Bootstrap 4.6.2升级到Bootstrap 5.3.6

功能特性：
1. 自动检测所有模板文件中的Bootstrap依赖
2. 批量替换Bootstrap CSS/JS引用
3. 更新第三方库的Bootstrap兼容版本
4. 处理Bootstrap 4到5的语法变更
5. 生成详细的迁移报告
6. 支持回滚操作

作者: Augment Agent
日期: 2025-06-15
"""

import os
import re
import json
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import logging

class Bootstrap536MigrationTool:
    """Bootstrap 5.3.6 迁移工具"""
    
    def __init__(self, project_root: str = "."):
        """初始化迁移工具"""
        self.project_root = Path(project_root).resolve()
        self.app_root = self.project_root / "app"
        self.static_root = self.app_root / "static"
        self.templates_root = self.app_root / "templates"
        
        # 备份目录
        self.backup_dir = self.project_root / f"bootstrap_migration_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 迁移配置
        self.migration_config = {
            "bootstrap_version": "5.3.6",
            "cdn_base": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist",
            "local_bootstrap_path": "bootstrap",
            "backup_enabled": True,
            "dry_run": False
        }
        
        # 文件替换映射
        self.css_replacements = {
            # Bootstrap CSS 文件替换
            "bootstrap/css/bootstrap.min.css": "bootstrap/css/bootstrap.min.css",
            "bootstrap/css/bootstrap.css": "bootstrap/css/bootstrap.css",
            "vendor/bootstrap/css/bootstrap.min.css": "bootstrap/css/bootstrap.min.css",
            
            # DataTables Bootstrap 兼容性
            "vendor/datatables/css/dataTables.bootstrap4.min.css": "vendor/datatables/css/dataTables.bootstrap5.min.css",
            
            # Select2 Bootstrap 兼容性
            "vendor/select2/css/select2-bootstrap4.min.css": "vendor/select2/css/select2-bootstrap5.min.css",
        }
        
        self.js_replacements = {
            # Bootstrap JS 文件替换
            "bootstrap/js/bootstrap.bundle.min.js": "bootstrap/js/bootstrap.bundle.min.js",
            "bootstrap/js/bootstrap.min.js": "bootstrap/js/bootstrap.min.js",
            "vendor/bootstrap/js/bootstrap.bundle.min.js": "bootstrap/js/bootstrap.bundle.min.js",
        }
        
        # Bootstrap 4 到 5 的类名映射
        self.class_mappings = {
            # 文本对齐
            "text-left": "text-start",
            "text-right": "text-end",
            
            # Flexbox
            "ml-auto": "ms-auto",
            "mr-auto": "me-auto",
            "ml-": "ms-",
            "mr-": "me-",
            "pl-": "ps-",
            "pr-": "pe-",
            
            # 表单
            "form-group": "mb-3",
            "form-row": "row g-3",
            
            # 按钮
            "btn-outline-": "btn-outline-",
            
            # 卡片
            "card-deck": "row row-cols-1 row-cols-md-3 g-4",
            
            # 导航
            "navbar-toggler-icon": "navbar-toggler-icon",
            
            # 工具类
            "sr-only": "visually-hidden",
            "font-weight-": "fw-",
            "font-italic": "fst-italic",
            
            # 间距
            "no-gutters": "g-0",
        }
        
        # 需要特殊处理的属性
        self.attribute_mappings = {
            'data-toggle': 'data-bs-toggle',
            'data-target': 'data-bs-target',
            'data-dismiss': 'data-bs-dismiss',
            'data-slide': 'data-bs-slide',
            'data-slide-to': 'data-bs-slide-to',
        }
        
        # 设置日志
        self.setup_logging()
        
        # 统计信息
        self.stats = {
            "files_processed": 0,
            "files_modified": 0,
            "css_replacements": 0,
            "js_replacements": 0,
            "class_replacements": 0,
            "attribute_replacements": 0,
            "errors": []
        }
    
    def setup_logging(self):
        """设置日志记录"""
        log_file = self.project_root / f"bootstrap_migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(__name__)
    
    def create_backup(self):
        """创建项目备份"""
        if not self.migration_config["backup_enabled"]:
            return
        
        self.logger.info(f"🔄 创建备份到: {self.backup_dir}")
        
        try:
            # 创建备份目录
            self.backup_dir.mkdir(exist_ok=True)
            
            # 备份模板文件
            templates_backup = self.backup_dir / "templates"
            if self.templates_root.exists():
                shutil.copytree(self.templates_root, templates_backup)
            
            # 备份静态文件
            static_backup = self.backup_dir / "static"
            if self.static_root.exists():
                shutil.copytree(self.static_root, static_backup)
            
            # 保存迁移配置
            config_file = self.backup_dir / "migration_config.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.migration_config, f, indent=2, ensure_ascii=False)
            
            self.logger.info("✅ 备份创建成功")
            
        except Exception as e:
            self.logger.error(f"❌ 备份创建失败: {str(e)}")
            raise
    
    def scan_template_files(self) -> List[Path]:
        """扫描所有模板文件"""
        template_files = []
        
        if not self.templates_root.exists():
            self.logger.warning("⚠️ 模板目录不存在")
            return template_files
        
        # 查找所有HTML文件
        for html_file in self.templates_root.rglob("*.html"):
            template_files.append(html_file)
        
        self.logger.info(f"📁 发现 {len(template_files)} 个模板文件")
        return template_files
    
    def analyze_css_dependencies(self, file_path: Path) -> Dict[str, List[str]]:
        """分析文件中的CSS依赖"""
        dependencies = {
            "bootstrap_css": [],
            "third_party_css": [],
            "custom_css": [],
            "inline_styles": []
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找CSS链接
            css_pattern = r'<link[^>]*rel=["\']stylesheet["\'][^>]*href=["\']([^"\']+)["\'][^>]*>'
            css_matches = re.findall(css_pattern, content, re.IGNORECASE)
            
            for css_url in css_matches:
                if 'bootstrap' in css_url.lower():
                    dependencies["bootstrap_css"].append(css_url)
                elif any(lib in css_url.lower() for lib in ['fontawesome', 'jquery', 'datatables', 'select2', 'toastr']):
                    dependencies["third_party_css"].append(css_url)
                else:
                    dependencies["custom_css"].append(css_url)
            
            # 查找内联样式
            style_pattern = r'<style[^>]*>(.*?)</style>'
            style_matches = re.findall(style_pattern, content, re.DOTALL | re.IGNORECASE)
            dependencies["inline_styles"] = style_matches
            
        except Exception as e:
            self.logger.error(f"❌ 分析文件 {file_path} 失败: {str(e)}")
            self.stats["errors"].append(f"分析文件失败: {file_path} - {str(e)}")
        
        return dependencies

    def replace_css_references(self, content: str) -> Tuple[str, int]:
        """替换CSS文件引用"""
        replacements_count = 0
        modified_content = content

        for old_path, new_path in self.css_replacements.items():
            # 构建替换模式
            pattern = re.escape(old_path)
            if pattern in modified_content:
                modified_content = modified_content.replace(old_path, new_path)
                replacements_count += modified_content.count(new_path) - content.count(new_path)
                self.logger.debug(f"🔄 替换CSS: {old_path} -> {new_path}")

        return modified_content, replacements_count

    def replace_js_references(self, content: str) -> Tuple[str, int]:
        """替换JS文件引用"""
        replacements_count = 0
        modified_content = content

        for old_path, new_path in self.js_replacements.items():
            pattern = re.escape(old_path)
            if pattern in modified_content:
                modified_content = modified_content.replace(old_path, new_path)
                replacements_count += modified_content.count(new_path) - content.count(new_path)
                self.logger.debug(f"🔄 替换JS: {old_path} -> {new_path}")

        return modified_content, replacements_count

    def replace_bootstrap_classes(self, content: str) -> Tuple[str, int]:
        """替换Bootstrap类名"""
        replacements_count = 0
        modified_content = content

        # 处理类名映射
        for old_class, new_class in self.class_mappings.items():
            # 使用正则表达式确保完整匹配类名
            if old_class.endswith('-'):
                # 处理前缀类名（如 ml-, mr-）
                pattern = rf'\b{re.escape(old_class)}(\d+)\b'
                replacement = rf'{new_class}\1'
                new_content = re.sub(pattern, replacement, modified_content)
                if new_content != modified_content:
                    count = len(re.findall(pattern, modified_content))
                    replacements_count += count
                    modified_content = new_content
                    self.logger.debug(f"🔄 替换类前缀: {old_class}* -> {new_class}* ({count}次)")
            else:
                # 处理完整类名
                pattern = rf'\b{re.escape(old_class)}\b'
                new_content = re.sub(pattern, new_class, modified_content)
                if new_content != modified_content:
                    count = len(re.findall(pattern, modified_content))
                    replacements_count += count
                    modified_content = new_content
                    self.logger.debug(f"🔄 替换类名: {old_class} -> {new_class} ({count}次)")

        return modified_content, replacements_count

    def replace_bootstrap_attributes(self, content: str) -> Tuple[str, int]:
        """替换Bootstrap属性"""
        replacements_count = 0
        modified_content = content

        for old_attr, new_attr in self.attribute_mappings.items():
            # 使用正则表达式替换属性
            pattern = rf'\b{re.escape(old_attr)}='
            replacement = f'{new_attr}='
            new_content = re.sub(pattern, replacement, modified_content)
            if new_content != modified_content:
                count = len(re.findall(pattern, modified_content))
                replacements_count += count
                modified_content = new_content
                self.logger.debug(f"🔄 替换属性: {old_attr} -> {new_attr} ({count}次)")

        return modified_content, replacements_count

    def update_bootstrap_version_comments(self, content: str) -> str:
        """更新Bootstrap版本注释"""
        # 更新版本注释
        version_patterns = [
            (r'Bootstrap v4\.\d+\.\d+', f'Bootstrap v{self.migration_config["bootstrap_version"]}'),
            (r'Bootstrap 4\.\d+\.\d+', f'Bootstrap {self.migration_config["bootstrap_version"]}'),
            (r'bootstrap@4\.\d+\.\d+', f'bootstrap@{self.migration_config["bootstrap_version"]}'),
        ]

        modified_content = content
        for pattern, replacement in version_patterns:
            modified_content = re.sub(pattern, replacement, modified_content)

        return modified_content

    def process_template_file(self, file_path: Path) -> bool:
        """处理单个模板文件"""
        try:
            self.logger.info(f"📝 处理文件: {file_path.relative_to(self.project_root)}")

            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()

            modified_content = original_content
            total_replacements = 0

            # 1. 替换CSS引用
            modified_content, css_count = self.replace_css_references(modified_content)
            total_replacements += css_count
            self.stats["css_replacements"] += css_count

            # 2. 替换JS引用
            modified_content, js_count = self.replace_js_references(modified_content)
            total_replacements += js_count
            self.stats["js_replacements"] += js_count

            # 3. 替换Bootstrap类名
            modified_content, class_count = self.replace_bootstrap_classes(modified_content)
            total_replacements += class_count
            self.stats["class_replacements"] += class_count

            # 4. 替换Bootstrap属性
            modified_content, attr_count = self.replace_bootstrap_attributes(modified_content)
            total_replacements += attr_count
            self.stats["attribute_replacements"] += attr_count

            # 5. 更新版本注释
            modified_content = self.update_bootstrap_version_comments(modified_content)

            # 检查是否有修改
            if modified_content != original_content:
                if not self.migration_config["dry_run"]:
                    # 写入修改后的内容
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(modified_content)

                self.stats["files_modified"] += 1
                self.logger.info(f"✅ 文件已更新: {total_replacements} 处修改")
                return True
            else:
                self.logger.info("ℹ️ 文件无需修改")
                return False

        except Exception as e:
            error_msg = f"处理文件失败: {file_path} - {str(e)}"
            self.logger.error(f"❌ {error_msg}")
            self.stats["errors"].append(error_msg)
            return False
        finally:
            self.stats["files_processed"] += 1

    def download_bootstrap_536(self):
        """下载Bootstrap 5.3.6文件"""
        bootstrap_dir = self.static_root / "bootstrap"

        self.logger.info("📥 准备下载Bootstrap 5.3.6...")

        # 创建Bootstrap目录
        bootstrap_dir.mkdir(exist_ok=True)
        (bootstrap_dir / "css").mkdir(exist_ok=True)
        (bootstrap_dir / "js").mkdir(exist_ok=True)

        # Bootstrap 5.3.6 文件URL
        files_to_download = {
            "css/bootstrap.min.css": f"{self.migration_config['cdn_base']}/css/bootstrap.min.css",
            "css/bootstrap.css": f"{self.migration_config['cdn_base']}/css/bootstrap.css",
            "css/bootstrap.min.css.map": f"{self.migration_config['cdn_base']}/css/bootstrap.min.css.map",
            "js/bootstrap.bundle.min.js": f"{self.migration_config['cdn_base']}/js/bootstrap.bundle.min.js",
            "js/bootstrap.min.js": f"{self.migration_config['cdn_base']}/js/bootstrap.min.js",
            "js/bootstrap.bundle.min.js.map": f"{self.migration_config['cdn_base']}/js/bootstrap.bundle.min.js.map",
        }

        try:
            import requests

            for local_path, url in files_to_download.items():
                file_path = bootstrap_dir / local_path

                self.logger.info(f"📥 下载: {url}")
                response = requests.get(url, timeout=30)
                response.raise_for_status()

                with open(file_path, 'wb') as f:
                    f.write(response.content)

                self.logger.info(f"✅ 已保存: {file_path}")

            self.logger.info("✅ Bootstrap 5.3.6 下载完成")

        except ImportError:
            self.logger.warning("⚠️ 未安装requests库，请手动下载Bootstrap 5.3.6文件")
            self.logger.info(f"📋 下载地址: {self.migration_config['cdn_base']}")

        except Exception as e:
            self.logger.error(f"❌ 下载Bootstrap失败: {str(e)}")
            raise

    def update_third_party_libraries(self):
        """更新第三方库的Bootstrap兼容版本"""
        self.logger.info("🔄 更新第三方库Bootstrap兼容性...")

        # DataTables Bootstrap 5 兼容文件
        datatables_dir = self.static_root / "vendor" / "datatables" / "css"
        if datatables_dir.exists():
            # 检查是否存在Bootstrap 4版本文件
            bs4_file = datatables_dir / "dataTables.bootstrap4.min.css"
            bs5_file = datatables_dir / "dataTables.bootstrap5.min.css"

            if bs4_file.exists() and not bs5_file.exists():
                # 复制Bootstrap 4版本作为Bootstrap 5版本的临时解决方案
                shutil.copy2(bs4_file, bs5_file)
                self.logger.info("✅ 创建DataTables Bootstrap 5兼容文件")

        # Select2 Bootstrap 5 兼容文件
        select2_dir = self.static_root / "vendor" / "select2" / "css"
        if select2_dir.exists():
            bs4_file = select2_dir / "select2-bootstrap4.min.css"
            bs5_file = select2_dir / "select2-bootstrap5.min.css"

            if bs4_file.exists() and not bs5_file.exists():
                shutil.copy2(bs4_file, bs5_file)
                self.logger.info("✅ 创建Select2 Bootstrap 5兼容文件")

    def generate_migration_report(self) -> Dict:
        """生成迁移报告"""
        report = {
            "migration_info": {
                "timestamp": datetime.now().isoformat(),
                "bootstrap_version": self.migration_config["bootstrap_version"],
                "project_root": str(self.project_root),
                "dry_run": self.migration_config["dry_run"]
            },
            "statistics": self.stats.copy(),
            "file_mappings": {
                "css_replacements": self.css_replacements,
                "js_replacements": self.js_replacements,
                "class_mappings": self.class_mappings,
                "attribute_mappings": self.attribute_mappings
            },
            "recommendations": []
        }

        # 添加建议
        if self.stats["files_modified"] > 0:
            report["recommendations"].append("建议在部署前进行全面测试")
            report["recommendations"].append("检查所有页面的布局和功能是否正常")

        if self.stats["errors"]:
            report["recommendations"].append("处理迁移过程中的错误")

        return report

    def save_migration_report(self, report: Dict):
        """保存迁移报告"""
        report_file = self.project_root / f"bootstrap_migration_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)

            self.logger.info(f"📊 迁移报告已保存: {report_file}")

        except Exception as e:
            self.logger.error(f"❌ 保存报告失败: {str(e)}")

    def print_summary(self, report: Dict):
        """打印迁移摘要"""
        print("\n" + "="*60)
        print("🚀 Bootstrap 5.3.6 迁移完成摘要")
        print("="*60)

        stats = report["statistics"]
        print(f"📁 处理文件总数: {stats['files_processed']}")
        print(f"✅ 修改文件数量: {stats['files_modified']}")
        print(f"🎨 CSS引用替换: {stats['css_replacements']}")
        print(f"📜 JS引用替换: {stats['js_replacements']}")
        print(f"🏷️ 类名替换: {stats['class_replacements']}")
        print(f"🔧 属性替换: {stats['attribute_replacements']}")

        if stats["errors"]:
            print(f"❌ 错误数量: {len(stats['errors'])}")
            print("\n错误详情:")
            for error in stats["errors"][:5]:  # 只显示前5个错误
                print(f"  • {error}")
            if len(stats["errors"]) > 5:
                print(f"  ... 还有 {len(stats['errors']) - 5} 个错误")

        if report["recommendations"]:
            print("\n💡 建议:")
            for rec in report["recommendations"]:
                print(f"  • {rec}")

        print("\n" + "="*60)

    def run_migration(self, dry_run: bool = False, download_bootstrap: bool = True):
        """执行迁移"""
        self.migration_config["dry_run"] = dry_run

        try:
            print("🚀 开始Bootstrap 5.3.6迁移...")

            if dry_run:
                print("🔍 运行模式: 预览模式（不会修改文件）")
            else:
                print("✏️ 运行模式: 实际修改模式")

            # 1. 创建备份
            if not dry_run:
                self.create_backup()

            # 2. 下载Bootstrap 5.3.6
            if download_bootstrap and not dry_run:
                self.download_bootstrap_536()

            # 3. 更新第三方库
            if not dry_run:
                self.update_third_party_libraries()

            # 4. 扫描模板文件
            template_files = self.scan_template_files()

            if not template_files:
                self.logger.warning("⚠️ 未找到模板文件")
                return

            # 5. 处理每个模板文件
            self.logger.info(f"🔄 开始处理 {len(template_files)} 个模板文件...")

            for file_path in template_files:
                self.process_template_file(file_path)

            # 6. 生成报告
            report = self.generate_migration_report()
            self.save_migration_report(report)
            self.print_summary(report)

            if dry_run:
                print("\n🔍 预览完成！使用 run_migration(dry_run=False) 执行实际迁移")
            else:
                print("\n✅ 迁移完成！请测试应用程序功能")

        except Exception as e:
            self.logger.error(f"❌ 迁移失败: {str(e)}")
            raise

    def rollback(self, backup_path: Optional[str] = None):
        """回滚迁移"""
        if backup_path:
            backup_dir = Path(backup_path)
        else:
            # 查找最新的备份
            backup_pattern = self.project_root / "bootstrap_migration_backup_*"
            backup_dirs = list(self.project_root.glob("bootstrap_migration_backup_*"))

            if not backup_dirs:
                self.logger.error("❌ 未找到备份目录")
                return

            backup_dir = max(backup_dirs, key=lambda x: x.stat().st_mtime)

        if not backup_dir.exists():
            self.logger.error(f"❌ 备份目录不存在: {backup_dir}")
            return

        try:
            self.logger.info(f"🔄 开始回滚，使用备份: {backup_dir}")

            # 恢复模板文件
            templates_backup = backup_dir / "templates"
            if templates_backup.exists():
                if self.templates_root.exists():
                    shutil.rmtree(self.templates_root)
                shutil.copytree(templates_backup, self.templates_root)
                self.logger.info("✅ 模板文件已恢复")

            # 恢复静态文件
            static_backup = backup_dir / "static"
            if static_backup.exists():
                if self.static_root.exists():
                    shutil.rmtree(self.static_root)
                shutil.copytree(static_backup, self.static_root)
                self.logger.info("✅ 静态文件已恢复")

            self.logger.info("✅ 回滚完成")

        except Exception as e:
            self.logger.error(f"❌ 回滚失败: {str(e)}")
            raise


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="Bootstrap 5.3.6 迁移工具")
    parser.add_argument("--project-root", default=".", help="项目根目录路径")
    parser.add_argument("--dry-run", action="store_true", help="预览模式，不修改文件")
    parser.add_argument("--no-download", action="store_true", help="不下载Bootstrap文件")
    parser.add_argument("--rollback", help="回滚到指定备份目录")

    args = parser.parse_args()

    # 创建迁移工具实例
    tool = Bootstrap536MigrationTool(args.project_root)

    if args.rollback:
        tool.rollback(args.rollback)
    else:
        tool.run_migration(
            dry_run=args.dry_run,
            download_bootstrap=not args.no_download
        )


if __name__ == "__main__":
    main()
