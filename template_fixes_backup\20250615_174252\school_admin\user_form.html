{% extends "base.html" %}
{% from '_formhelpers.html' import render_field %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ title }}</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('school_admin.users') }}" class="btn btn-sm btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回用户列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" novalidate novalidate>                        {{ form.hidden_tag() }}
                        <div class="row">
                            <div class="col-md-6">
                                {{ render_field(form.username, class="form-control") }}
                            </div>
                            <div class="col-md-6">
                                {{ render_field(form.email, class="form-control") }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                {{ render_field(form.real_name, class="form-control") }}
                            </div>
                            <div class="col-md-6">
                                {{ render_field(form.phone, class="form-control") }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                {{ render_field(form.password, class="form-control") }}
                                {% if user %}
                                <small class="form-text text-muted">如果不需要修改密码，请留空</small>
                                {% else %}
                                <small class="form-text text-muted">如果留空，将使用默认密码：123456</small>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                {{ render_field(form.password2, class="form-control") }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <!-- 移除子区域选择，用户直接属于学校 -->
                                <div class="mb-3">
                                    <label>所属区域</label>
                                    <input type="text" class="form-control" value="{{ current_user.area.name }}" readonly>
                                    <small class="form-text text-muted">用户将直接属于当前学校，无需选择子区域</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                {{ render_field(form.status, class="form-control") }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label">角色选择</label>
                                    <div class="card">
                                        <div class="card-body">
                                            <div class="row">
                                                {% for role_id, role_name in form.roles.choices %}
                                                <div class="col-md-4 mb-2">
                                                    <div class="form-check">
                                                        <input type="checkbox" class="form-check-input role-checkbox"
                                                               id="role_{{ role_id }}" value="{{ role_id }}"
                                                               {% if form.roles.data and role_id in form.roles.data %}checked{% endif %}
                                                               data-role-name="{{ role_name }}">
                                                        <label class="form-check-label" for="role_{{ role_id }}">{{ role_name }}</label>
                                                    </div>
                                                </div>
                                                {% endfor %}
                                            </div>
                                        </div>
                                    </div>
                                    <!-- 隐藏的多选字段，用于存储选中的角色ID -->
                                    {{ form.roles(class="d-none", id="roles_hidden") }}
                                    {% if form.roles.errors %}
                                    <div class="invalid-feedback" style="display: block;">
                                        {% for error in form.roles.errors %}
                                        <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">勾选需要分配的角色，可以选择多个</small>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    {{ form.submit(class="btn btn-primary") }}
                                    <a href="{{ url_for('school_admin.users') }}" class="btn btn-secondary">取消</a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    // 角色默认权限和模块可见性配置
    const roleDefaults = {
        // 仓库管理员
        '仓库管理员': {
            description: '负责管理仓库和库存',
            modules: ['inventory', 'stock_in', 'stock_out', 'supplier', 'material_batch'],
            permissions: {
                'inventory': ['view', 'create', 'edit', 'delete'],
                'stock_in': ['view', 'create', 'edit', 'delete'],
                'stock_out': ['view', 'create', 'edit', 'delete'],
                'supplier': ['view'],
                'material_batch': ['view', 'create', 'edit']
            }
        },
        // 食堂管理员
        '食堂管理员': {
            description: '负责管理食堂日常运营',
            modules: ['daily_management', 'menu', 'recipe', 'weekly_menu', 'sample'],
            permissions: {
                'daily_management': ['view', 'create', 'edit', 'delete'],
                'menu': ['view', 'create', 'edit'],
                'recipe': ['view', 'create', 'edit'],
                'weekly_menu': ['view', 'create', 'edit'],
                'sample': ['view', 'create', 'edit']
            }
        },
        // 采购员
        '采购员': {
            description: '负责采购管理',
            modules: ['purchase', 'purchase_order_list', 'purchase_order_create', 'supplier_list', 'supplier_product_list'],
            permissions: {
                'purchase': ['view', 'create', 'edit'],
                'supplier': ['view', 'create', 'edit']
            }
        },
        // 学校管理员
        '学校管理员': {
            description: '拥有学校级别的管理权限',
            modules: ['user', 'area', 'supplier', 'ingredient', 'menu', 'menu_plan', 'weekly_menu', 'sample', 'traceability', 'daily_management', 'report'],
            permissions: {
                'user': ['view', 'create', 'edit', 'delete'],
                'area': ['view'],
                'supplier': ['view', 'create', 'edit'],
                'ingredient': ['view', 'create', 'edit'],
                'menu': ['view', 'approve'],
                'menu_plan': ['view', 'approve'],
                'weekly_menu': ['view', 'approve', 'publish'],
                'sample': ['view'],
                'traceability': ['view'],
                'daily_management': ['view', 'create', 'edit'],
                'report': ['view', 'export', 'print']
            }
        }
    };

    $(document).ready(function() {
        // 初始化角色选择
        updateHiddenRolesField();

        // 监听角色复选框变化
        $('.role-checkbox').change(function() {
            updateHiddenRolesField();

            // 显示角色信息
            const roleName = $(this).data('role-name');
            const isChecked = $(this).prop('checked');

            if (isChecked && roleDefaults[roleName]) {
                // 显示角色默认权限信息
                const roleInfo = roleDefaults[roleName];

                // 将英文模块名转换为中文
                const moduleMapping = {
                    'daily_management': '食堂日常管理',
                    'menu': '食谱管理',
                    'recipe': '食谱管理',
                    'weekly_menu': '周菜单管理',
                    'sample': '留样管理',
                    'inventory': '库存管理',
                    'stock_in': '入库管理',
                    'stock_out': '出库管理',
                    'supplier': '供应商管理',
                    'material_batch': '批次管理',
                    'purchase': '采购管理',
                    'purchase_order_list': '采购订单列表',
                    'purchase_order_create': '采购订单创建',
                    'supplier_list': '供应商列表',
                    'supplier_product_list': '供应商产品列表',
                    'user': '用户管理',
                    'area': '区域管理',
                    'ingredient': '食材管理',
                    'menu_plan': '菜单计划管理',
                    'traceability': '食材溯源与留样',
                    'report': '报表管理'
                };

                const chineseModules = roleInfo.modules.map(module => moduleMapping[module] || module);

                const message = `已选择 <strong>${roleName}</strong>: ${roleInfo.description}<br>
                                此角色默认拥有以下模块权限: ${chineseModules.join(', ')}`;

                // 使用Bootstrap的toast或alert显示信息
                showToast(message, 'info');
            }
        });

        // 表单提交前验证
        $('form').submit(function(e) {
            // 确保至少选择了一个角色
            if ($('.role-checkbox:checked').length === 0) {
                e.preventDefault();
                showToast('请至少选择一个角色', 'warning');
                return false;
            }

            // 更新隐藏字段
            updateHiddenRolesField();
            return true;
        });
    });

    // 更新隐藏的角色字段
    function updateHiddenRolesField() {
        const selectedRoles = [];
        $('.role-checkbox:checked').each(function() {
            selectedRoles.push($(this).val());
        });

        // 更新隐藏字段的值
        $('#roles_hidden').val(selectedRoles);
    }

    // 显示提示信息
    function showToast(message, type = 'info') {
        // 创建或获取toast容器
        let toastContainer = $('#toast-container');
        if (toastContainer.length === 0) {
            $('body').append('<div id="toast-container" style="position: fixed; top: 20px; right: 20px; z-index: 9999;"></div>');
            toastContainer = $('#toast-container');
        }

        // 创建toast元素
        const toastId = 'toast-' + Date.now();
        const toast = `
            <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true" data-delay="5000">
                <div class="toast-header bg-${type}">
                    <strong class="me-auto text-white">角色信息</strong>
                    <button type="button" class="ms-2 mb-1 close" data-bs-dismiss="toast" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;

        // 添加到容器并显示
        toastContainer.append(toast);
        $(`#${toastId}`).toast('show');

        // 5秒后自动移除
        setTimeout(() => {
            $(`#${toastId}`).remove();
        }, 5000);
    }
</script>
{% endblock %}
