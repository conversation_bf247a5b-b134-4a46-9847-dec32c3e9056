trio-0.27.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
trio-0.27.0.dist-info/LICENSE,sha256=QY0CXhKEMR8mkCY-bvpr9RWF5XQYGOzmPlhiSH5QW7k,190
trio-0.27.0.dist-info/LICENSE.APACHE2,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
trio-0.27.0.dist-info/LICENSE.MIT,sha256=-qMB1y3MAjtDK9d9wIp3PKNEnlwnRAudZutG-4UAtDA,1091
trio-0.27.0.dist-info/METADATA,sha256=camlA-ZBKm-ZUenakOb8xkiKrLTdOszIybBshWvlPvk,8599
trio-0.27.0.dist-info/RECORD,,
trio-0.27.0.dist-info/WHEEL,sha256=OVMc5UfuAQiSplgO0_WdW7vXVGAt9Hdd6qtN4HotdyA,91
trio-0.27.0.dist-info/entry_points.txt,sha256=wuxwk2BfNjtLCdKM6ypHa2i9iCWoQCzLeckmVTaqNEQ,61
trio-0.27.0.dist-info/top_level.txt,sha256=_le_BDvZ_wML19n4VV0F5vMuqlucn3S2WDj34dDY_Vo,5
trio/__init__.py,sha256=j-CsB3YpuEe-yLDEGu0GyC9GBZLeR0ZbePJcAjJ6i54,4935
trio/__main__.py,sha256=FTkXoLLuHaIqOgu1yBGldWejlhWiHR12cvYcpxcRohk,44
trio/__pycache__/__init__.cpython-38.pyc,,
trio/__pycache__/__main__.cpython-38.pyc,,
trio/__pycache__/_abc.cpython-38.pyc,,
trio/__pycache__/_channel.cpython-38.pyc,,
trio/__pycache__/_deprecate.cpython-38.pyc,,
trio/__pycache__/_dtls.cpython-38.pyc,,
trio/__pycache__/_file_io.cpython-38.pyc,,
trio/__pycache__/_highlevel_generic.cpython-38.pyc,,
trio/__pycache__/_highlevel_open_tcp_listeners.cpython-38.pyc,,
trio/__pycache__/_highlevel_open_tcp_stream.cpython-38.pyc,,
trio/__pycache__/_highlevel_open_unix_stream.cpython-38.pyc,,
trio/__pycache__/_highlevel_serve_listeners.cpython-38.pyc,,
trio/__pycache__/_highlevel_socket.cpython-38.pyc,,
trio/__pycache__/_highlevel_ssl_helpers.cpython-38.pyc,,
trio/__pycache__/_path.cpython-38.pyc,,
trio/__pycache__/_repl.cpython-38.pyc,,
trio/__pycache__/_signals.cpython-38.pyc,,
trio/__pycache__/_socket.cpython-38.pyc,,
trio/__pycache__/_ssl.cpython-38.pyc,,
trio/__pycache__/_subprocess.cpython-38.pyc,,
trio/__pycache__/_sync.cpython-38.pyc,,
trio/__pycache__/_threads.cpython-38.pyc,,
trio/__pycache__/_timeouts.cpython-38.pyc,,
trio/__pycache__/_unix_pipes.cpython-38.pyc,,
trio/__pycache__/_util.cpython-38.pyc,,
trio/__pycache__/_version.cpython-38.pyc,,
trio/__pycache__/_wait_for_object.cpython-38.pyc,,
trio/__pycache__/_windows_pipes.cpython-38.pyc,,
trio/__pycache__/abc.cpython-38.pyc,,
trio/__pycache__/from_thread.cpython-38.pyc,,
trio/__pycache__/lowlevel.cpython-38.pyc,,
trio/__pycache__/socket.cpython-38.pyc,,
trio/__pycache__/to_thread.cpython-38.pyc,,
trio/_abc.py,sha256=_LVMlUO5bBs16D1dmQZvhdg0QgRQ2wejt500InPmf_o,25642
trio/_channel.py,sha256=oCe_5F8UfsJH6Z2i3A9wfzXdOIbYqJJsVqjLMtZaAxQ,17105
trio/_core/__init__.py,sha256=NDjcsMI_vauHixio8EX0x0k9Rz14pZ4Ab-YbZz82Me0,2204
trio/_core/__pycache__/__init__.cpython-38.pyc,,
trio/_core/__pycache__/_asyncgens.cpython-38.pyc,,
trio/_core/__pycache__/_concat_tb.cpython-38.pyc,,
trio/_core/__pycache__/_entry_queue.cpython-38.pyc,,
trio/_core/__pycache__/_exceptions.cpython-38.pyc,,
trio/_core/__pycache__/_generated_instrumentation.cpython-38.pyc,,
trio/_core/__pycache__/_generated_io_epoll.cpython-38.pyc,,
trio/_core/__pycache__/_generated_io_kqueue.cpython-38.pyc,,
trio/_core/__pycache__/_generated_io_windows.cpython-38.pyc,,
trio/_core/__pycache__/_generated_run.cpython-38.pyc,,
trio/_core/__pycache__/_instrumentation.cpython-38.pyc,,
trio/_core/__pycache__/_io_common.cpython-38.pyc,,
trio/_core/__pycache__/_io_epoll.cpython-38.pyc,,
trio/_core/__pycache__/_io_kqueue.cpython-38.pyc,,
trio/_core/__pycache__/_io_windows.cpython-38.pyc,,
trio/_core/__pycache__/_ki.cpython-38.pyc,,
trio/_core/__pycache__/_local.cpython-38.pyc,,
trio/_core/__pycache__/_mock_clock.cpython-38.pyc,,
trio/_core/__pycache__/_parking_lot.cpython-38.pyc,,
trio/_core/__pycache__/_run.cpython-38.pyc,,
trio/_core/__pycache__/_thread_cache.cpython-38.pyc,,
trio/_core/__pycache__/_traps.cpython-38.pyc,,
trio/_core/__pycache__/_unbounded_queue.cpython-38.pyc,,
trio/_core/__pycache__/_wakeup_socketpair.cpython-38.pyc,,
trio/_core/__pycache__/_windows_cffi.cpython-38.pyc,,
trio/_core/_asyncgens.py,sha256=0OcnWQp2BIJEpawIayXs9FWFpRU1jWVxQck5FykTd1U,9877
trio/_core/_concat_tb.py,sha256=28rgJydhA4DUFupQThlH-qDmpSSSgUg55IwtGgvHaOg,5294
trio/_core/_entry_queue.py,sha256=IFsUYxjneZwR8ZPp-YXWdXjd09OTOXnnYwOlCdgdwXg,9306
trio/_core/_exceptions.py,sha256=t362dVCl96rTQ8NG5J24e_HSF8enTHA0pMEtwdzOW9k,4173
trio/_core/_generated_instrumentation.py,sha256=V5rZ0IW3urDOKrfZSrYZDDcPBaR-AT8MrL0ytQ9sytU,1746
trio/_core/_generated_io_epoll.py,sha256=xKPLm2fVz5uggkGu7Ng4x9fshIJOc7K_eq5cO5EqgjU,4006
trio/_core/_generated_io_kqueue.py,sha256=B3BaeJs6rQCyfEFsEb25Tuv6pQZm_Bez3lpXYuhpL_g,5811
trio/_core/_generated_io_windows.py,sha256=MhifgsZcU_-RFzYrUjLmNS0Wh3O_SKdvopy1TvdJIIE,7965
trio/_core/_generated_run.py,sha256=3Kd1XOjjjvA0LyQy-3WYJL6w3vJLh0zuPwoi7nzlVJc,10577
trio/_core/_instrumentation.py,sha256=XOM0HgQHl3S4qEUuHTVHUuG66OB6M9d4-ydZBtEZVVk,3775
trio/_core/_io_common.py,sha256=DK3lqNPV5g4VPDoo7VF4fXRITIMFAzJlXBGmrhsA85U,857
trio/_core/_io_epoll.py,sha256=A-ozH_2YW3ZEPo5lIKLglxmbmtTHV8N3_wqXSGzhxnY,17841
trio/_core/_io_kqueue.py,sha256=24fhTQMcJCtilCIbNJtyKHmY5m3Ff5mH1tBOlqJ4v-Q,11451
trio/_core/_io_windows.py,sha256=EM9-_lNRenkkO_WJd2Rq7q2c9CdMGjfSgNoXsyR66TU,43367
trio/_core/_ki.py,sha256=BEj7CI9-2rWfBJTE5Le5RlsUBnyAF0p1w68VJCS23ns,9990
trio/_core/_local.py,sha256=9KNN8D3cepTlip1-IcqkyLhFPv5CaxBcny3ZJtwnuzA,3144
trio/_core/_mock_clock.py,sha256=okC62pAgj_k3sHgnmpGr4u6liNo3CkSZtgVE95VfONQ,6305
trio/_core/_parking_lot.py,sha256=7UcV3Gq1hYSWPgTtI4UiUoiDs-WoTJES4NbJeLh2vpQ,11972
trio/_core/_run.py,sha256=dpaJDx6HFgXwTQ03F4E7s6yKrey7X5DyIp0ioYLrddY,121498
trio/_core/_tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
trio/_core/_tests/__pycache__/__init__.cpython-38.pyc,,
trio/_core/_tests/__pycache__/test_asyncgen.cpython-38.pyc,,
trio/_core/_tests/__pycache__/test_exceptiongroup_gc.cpython-38.pyc,,
trio/_core/_tests/__pycache__/test_guest_mode.cpython-38.pyc,,
trio/_core/_tests/__pycache__/test_instrumentation.cpython-38.pyc,,
trio/_core/_tests/__pycache__/test_io.cpython-38.pyc,,
trio/_core/_tests/__pycache__/test_ki.cpython-38.pyc,,
trio/_core/_tests/__pycache__/test_local.cpython-38.pyc,,
trio/_core/_tests/__pycache__/test_mock_clock.cpython-38.pyc,,
trio/_core/_tests/__pycache__/test_parking_lot.cpython-38.pyc,,
trio/_core/_tests/__pycache__/test_run.cpython-38.pyc,,
trio/_core/_tests/__pycache__/test_thread_cache.cpython-38.pyc,,
trio/_core/_tests/__pycache__/test_tutil.cpython-38.pyc,,
trio/_core/_tests/__pycache__/test_unbounded_queue.cpython-38.pyc,,
trio/_core/_tests/__pycache__/test_windows.cpython-38.pyc,,
trio/_core/_tests/__pycache__/tutil.cpython-38.pyc,,
trio/_core/_tests/test_asyncgen.py,sha256=3F_7USJS3UBhxjDieO5iotsq-VshoT_4fO7XC6YZRS4,11448
trio/_core/_tests/test_exceptiongroup_gc.py,sha256=0qUX7ZCzG4HVBdntkvayh3oan0fS5bi3krfp0_l8840,2814
trio/_core/_tests/test_guest_mode.py,sha256=IoY47XRPQYowzJWLn2b5-KZNARUNXVqY0Ygf1d2xFKQ,22527
trio/_core/_tests/test_instrumentation.py,sha256=5Ip2E_RgrS6gG1jIOf1Bp3jEsJso4w3z9XL5ZG50MwE,8346
trio/_core/_tests/test_io.py,sha256=7WabORkQQcK79_AHu4rkXAXfDwKQJbUb02BPVrM9t14,16167
trio/_core/_tests/test_ki.py,sha256=5OpxhKihhagnflfmva5-89I_24kMSgZctp4wW1uGSEI,16287
trio/_core/_tests/test_local.py,sha256=SqFyZb2OGxVsm6arn3E92fGa5cmL50iUfu-a69Vd2Bg,2889
trio/_core/_tests/test_mock_clock.py,sha256=KFz4ltZFEEw1j0FdQaprImWIJVhFWaNQDx_Cp9sOETg,5207
trio/_core/_tests/test_parking_lot.py,sha256=V7hgUDkGAMzA6r5XkaBpeB5izAnr2PHpNK-sIqxViHY,11728
trio/_core/_tests/test_run.py,sha256=CmJYSVqvQ9MvBY9ltGkPLv1GnNpb8LCvjwjfDJkUaro,95356
trio/_core/_tests/test_thread_cache.py,sha256=OhaRMdN_z_oCKbVYnSsW9ur_Ur4r4HGiKQD8ZeRo6Hg,6057
trio/_core/_tests/test_tutil.py,sha256=fNjIdaMx5pVYGC8GMU0-YRhiC4ZZFrY-iRKBfaQ9lK0,459
trio/_core/_tests/test_unbounded_queue.py,sha256=eRHRokbqAtF6k0u7NfsmStZ5Yap_joM_q2x4KrxkYIo,4323
trio/_core/_tests/test_windows.py,sha256=bff9_BkX87vvfQg9svVj6n4BPmB3wvmmciCAbWdpnhU,10334
trio/_core/_tests/tutil.py,sha256=h_Ve5J23LRLn-N6QC9gjVJF51C-x7MtvnyEdRh6YIy4,4021
trio/_core/_tests/type_tests/__pycache__/nursery_start.cpython-38.pyc,,
trio/_core/_tests/type_tests/__pycache__/run.cpython-38.pyc,,
trio/_core/_tests/type_tests/nursery_start.py,sha256=opvTjJTmgpd9curfI9xEhDPoeAEGc4xVR_0VxnqpcfE,2029
trio/_core/_tests/type_tests/run.py,sha256=IlznuwiOYdTCz2BgAeWkmKdM-_ApH0L8pstYUSK_UDA,1056
trio/_core/_thread_cache.py,sha256=QW0ai9wOhZy9MtFaSYs-qyt-DOBTb5Vg_ZZixbEXnIY,11283
trio/_core/_traps.py,sha256=rf4QdcWUcHfRjBAf7MV_foE7uxXgDrAzwtQlj6f7Ea8,11798
trio/_core/_unbounded_queue.py,sha256=24Y9cLGyilwLHgGon1f7lepYBmJmLoKRCi66fPTlQ0A,5026
trio/_core/_wakeup_socketpair.py,sha256=9nNUfcuDN9DMJ6ie53GrlPzpn565ZVPQe2JSrSymbrY,2882
trio/_core/_windows_cffi.py,sha256=YB23HkLHPLGp50OrJrnAdVCuEC8lrPOZ4cOFaSrKJYQ,13905
trio/_deprecate.py,sha256=YE69_cA39COTOdMhVeKrHPOhvDLB9CtFSdFreAOb9MQ,5790
trio/_dtls.py,sha256=XJqgEkZQh4KSvN1BH7uQI1XtnF5rdDPISRrEEJBpPXY,55061
trio/_file_io.py,sha256=YoUhDJ4ymWBEJEl5ucVqnup2ga6i1bTEqhBuk9GBsKo,15438
trio/_highlevel_generic.py,sha256=bt1WZ-YCZ7Ufveav4G_PsZj9KcheoZDwoNdwXS-rh-w,4759
trio/_highlevel_open_tcp_listeners.py,sha256=2dxhtvdYsC1vkArz33oyo5XhvnXMrMQDai-9v_Ak57I,9955
trio/_highlevel_open_tcp_stream.py,sha256=vTR-rlAK0y1Q2ik1nFClgFQT7koJoSjBPiVEb2y_tt0,18727
trio/_highlevel_open_unix_stream.py,sha256=haBGh2KVsfkjsBL3Y6gzqvZBWREnx4N3X2x1WpMGFAU,1625
trio/_highlevel_serve_listeners.py,sha256=p33e2SRnn0osbUsZnFSEGxK4zxPK8wCKPEZTuSFbaIk,5099
trio/_highlevel_socket.py,sha256=OGH1yKFjsJceqpZrRAaJWX_60mK3idinWeEwfdQUjvc,15733
trio/_highlevel_ssl_helpers.py,sha256=m_HzevvF6BVX4RwX_Qq4376c7DxsWoE8wjVu1Rb9jHY,6535
trio/_path.py,sha256=4ZP9AcOnm8dczkE_VydDCdzZTMDnm0WgfN6I9LhRnnE,8907
trio/_repl.py,sha256=0iWw6rMXbRiOTYPokrFFBpVMuxQpIf6C_u5TfQi2ecU,3384
trio/_signals.py,sha256=vGxZTQt8JppfeBidVXieCX51TO21mv3SS5JoVlirs1E,7187
trio/_socket.py,sha256=kNExKx_BOuOQOju2K9mfEd69ilUnQyE5iqbLxxQn6gc,45580
trio/_ssl.py,sha256=3RZp48Izj7Yl5YiqkCQT6KxpMvSvE-OfJW7K--x34-A,45601
trio/_subprocess.py,sha256=kqNdFdKwQH1xAmUHGACSYJxX-3_720eHv33BqxohrU8,53664
trio/_subprocess_platform/__init__.py,sha256=Eo-5559E1gSfihx6VvQEulF6LQfmLKKPgaoko3AvCVk,4561
trio/_subprocess_platform/__pycache__/__init__.cpython-38.pyc,,
trio/_subprocess_platform/__pycache__/kqueue.cpython-38.pyc,,
trio/_subprocess_platform/__pycache__/waitid.cpython-38.pyc,,
trio/_subprocess_platform/__pycache__/windows.cpython-38.pyc,,
trio/_subprocess_platform/kqueue.py,sha256=td6A0t9RsCNLLayzFBidGKMvVF3EOG91jfZ2JdYmMhA,1825
trio/_subprocess_platform/waitid.py,sha256=DyaOqrHY4mPml8GUM6F_zHuuRyXJxJjyN5xYr1fHNww,3888
trio/_subprocess_platform/windows.py,sha256=cHF_0YKclShFtFEEMblam28W4OIqU5X7WoZJbtvVNpI,365
trio/_sync.py,sha256=DCeAe_gDqb5VYIHH3lzGcAkZiFEC1okeL0PITyWEXn0,31368
trio/_tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
trio/_tests/__pycache__/__init__.cpython-38.pyc,,
trio/_tests/__pycache__/check_type_completeness.cpython-38.pyc,,
trio/_tests/__pycache__/module_with_deprecations.cpython-38.pyc,,
trio/_tests/__pycache__/pytest_plugin.cpython-38.pyc,,
trio/_tests/__pycache__/test_abc.cpython-38.pyc,,
trio/_tests/__pycache__/test_channel.cpython-38.pyc,,
trio/_tests/__pycache__/test_contextvars.cpython-38.pyc,,
trio/_tests/__pycache__/test_deprecate.cpython-38.pyc,,
trio/_tests/__pycache__/test_deprecate_strict_exception_groups_false.cpython-38.pyc,,
trio/_tests/__pycache__/test_dtls.cpython-38.pyc,,
trio/_tests/__pycache__/test_exports.cpython-38.pyc,,
trio/_tests/__pycache__/test_fakenet.cpython-38.pyc,,
trio/_tests/__pycache__/test_file_io.cpython-38.pyc,,
trio/_tests/__pycache__/test_highlevel_generic.cpython-38.pyc,,
trio/_tests/__pycache__/test_highlevel_open_tcp_listeners.cpython-38.pyc,,
trio/_tests/__pycache__/test_highlevel_open_tcp_stream.cpython-38.pyc,,
trio/_tests/__pycache__/test_highlevel_open_unix_stream.cpython-38.pyc,,
trio/_tests/__pycache__/test_highlevel_serve_listeners.cpython-38.pyc,,
trio/_tests/__pycache__/test_highlevel_socket.cpython-38.pyc,,
trio/_tests/__pycache__/test_highlevel_ssl_helpers.cpython-38.pyc,,
trio/_tests/__pycache__/test_path.cpython-38.pyc,,
trio/_tests/__pycache__/test_repl.cpython-38.pyc,,
trio/_tests/__pycache__/test_scheduler_determinism.cpython-38.pyc,,
trio/_tests/__pycache__/test_signals.cpython-38.pyc,,
trio/_tests/__pycache__/test_socket.cpython-38.pyc,,
trio/_tests/__pycache__/test_ssl.cpython-38.pyc,,
trio/_tests/__pycache__/test_subprocess.cpython-38.pyc,,
trio/_tests/__pycache__/test_sync.cpython-38.pyc,,
trio/_tests/__pycache__/test_testing.cpython-38.pyc,,
trio/_tests/__pycache__/test_testing_raisesgroup.cpython-38.pyc,,
trio/_tests/__pycache__/test_threads.cpython-38.pyc,,
trio/_tests/__pycache__/test_timeouts.cpython-38.pyc,,
trio/_tests/__pycache__/test_tracing.cpython-38.pyc,,
trio/_tests/__pycache__/test_trio.cpython-38.pyc,,
trio/_tests/__pycache__/test_unix_pipes.cpython-38.pyc,,
trio/_tests/__pycache__/test_util.cpython-38.pyc,,
trio/_tests/__pycache__/test_wait_for_object.cpython-38.pyc,,
trio/_tests/__pycache__/test_windows_pipes.cpython-38.pyc,,
trio/_tests/check_type_completeness.py,sha256=NEELl7wGTJW0GtrEqw2R28TwyuguepalNmzxou3-324,9821
trio/_tests/module_with_deprecations.py,sha256=-70KI43-pydpHBoQ1aZ2Hq1C7bAvVl2HLTPD5mHdHyI,626
trio/_tests/pytest_plugin.py,sha256=D1-5awR9YAXGw9topfLy_cshlgyPpyIM5hWXUn1YzU8,1581
trio/_tests/test_abc.py,sha256=0thACNs2xJoOiJnTw7J1EbMoeRSA4Bli1bIni-idjoA,2034
trio/_tests/test_channel.py,sha256=FCxf-TlCwOxQg9gXM0lf8Ow3b5jeXJnunKHH61K8jwg,12991
trio/_tests/test_contextvars.py,sha256=EAhqYyjMKQORPmF-zfZ0kOhR118vTFKwN1S6HndLtOk,1534
trio/_tests/test_deprecate.py,sha256=s3JGd-xt8g-vQ2kEt5MFEz1h_ZKKugldE--9IPYvRMU,8247
trio/_tests/test_deprecate_strict_exception_groups_false.py,sha256=0zg9esmjk2yVJRf4Emm_SoItFjJN4iUQ8zQaJxdpkag,1866
trio/_tests/test_dtls.py,sha256=OE7Q9smGF8egxmlHfycKezphN8iofz4yYi3xMlTbYSY,34098
trio/_tests/test_exports.py,sha256=HY_LK_Oa3EyvQtCb8LKmCRyMe7JzTdrIQGxzLwBbW4Y,20883
trio/_tests/test_fakenet.py,sha256=TZ1rZdr40ZZWwOQhb4L4i51g2OAnR-GOk5wzwI-jxs4,9778
trio/_tests/test_file_io.py,sha256=SIZo-C9FJi8wria2T6TQoCtVEgVDxuZXbjG_RGyGcgE,7722
trio/_tests/test_highlevel_generic.py,sha256=W0FvCC6g-18bbiCz1Jw3z8zPAyVIv366EyDZMOLQxf8,3035
trio/_tests/test_highlevel_open_tcp_listeners.py,sha256=jHavlW5v5AG17J89s9JCbbicoe9gx_ylibU68_T2Ml4,13241
trio/_tests/test_highlevel_open_tcp_stream.py,sha256=NiIhUs7SDXzrsS2IsWvD1suSkmeiDZkIMC1TB103wc8,22287
trio/_tests/test_highlevel_open_unix_stream.py,sha256=wmFHT4hyYz-efv1_s7GW6PltLDXldBmm1cDTW53HGW8,2444
trio/_tests/test_highlevel_serve_listeners.py,sha256=BGYokAnxnvF2rHGHqSu2S7e1IqEGmW4Y899JHu39pQ0,5923
trio/_tests/test_highlevel_socket.py,sha256=5OI6eGIJVrASnES6oyaa8hILRBomjjN14vFlieNPOBw,10959
trio/_tests/test_highlevel_ssl_helpers.py,sha256=LU99WKlRC8Kn89v2SDotG1uppxy5b1w61XqxmVp8KrU,5673
trio/_tests/test_path.py,sha256=QHsnOVcO5RVjY3a9lYM0n1uQtKlbZo18GYuMK1T27v0,7813
trio/_tests/test_repl.py,sha256=liepLr_cOhQAKtIpfWehITc_Eyw5t8vOYZBUwJHyX5M,7725
trio/_tests/test_scheduler_determinism.py,sha256=ySfsn09sUJHbfnkJTzptGZB-CS_zaJvZTtEhBQutk9s,1320
trio/_tests/test_signals.py,sha256=uE9Vx4ekL-0k7OuQLAjbJuSOfMQb9O3s6pNFYLERX1o,7442
trio/_tests/test_socket.py,sha256=7r9h8ti3MI_AHBi15MK90NHCNoEOlGsA9IxEIafV9dU,42212
trio/_tests/test_ssl.py,sha256=a2xtOy4cvmnEXzJdxu8OBu0SZDaJ8dszyvEo4uzZDfY,50935
trio/_tests/test_subprocess.py,sha256=9tbI2pLcl7hSVkKj8qabevzhzNjrgZvDmCiRiJIxvQQ,23699
trio/_tests/test_sync.py,sha256=u0A38-za1x6nFDPd2ibmfJHr4mLXAyTLW2nHmO-LYGg,19421
trio/_tests/test_testing.py,sha256=JV5YNDXerayW-SzmHffQT0qhWfi2TsL1_fQDQpNjJEY,20801
trio/_tests/test_testing_raisesgroup.py,sha256=6gMRTH9Zh8N7tfgo-C1qNxbNXUSupAepQ7He6DQskow,13722
trio/_tests/test_threads.py,sha256=deLoEAQj0Eafai4LqYGqDSxSWna1gHXFdRUy3oRPZRs,39421
trio/_tests/test_timeouts.py,sha256=83vuyWw45fQCZY51X2qRvjbUCHTsCT1LDe0FBT9p4w0,8459
trio/_tests/test_tracing.py,sha256=xY1kQq6ADqLfL49xosfOpO7oU5eEd8IEGBi0h4R0-tU,1696
trio/_tests/test_trio.py,sha256=iGi-6pXnwgr3H0FZkzJtoKme_zWvaxK81ogogqxkdlI,205
trio/_tests/test_unix_pipes.py,sha256=g63RRkA1DeueSAz3RXOwT2L6nuPew9Y1x7qPgL5-apM,10087
trio/_tests/test_util.py,sha256=MO0aDasGKMpV3Zu1msVbQBjzVTmMgg_At-srfItoNnI,9594
trio/_tests/test_wait_for_object.py,sha256=tKni_9yYgAl9u5c0Qj7tq52hgJvegHfVzXfmMghVZec,8340
trio/_tests/test_windows_pipes.py,sha256=HydxlZCHBBS1ddrd9GKyY5HowY3jkcI9iw5zTJWbosc,3353
trio/_tests/tools/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
trio/_tests/tools/__pycache__/__init__.cpython-38.pyc,,
trio/_tests/tools/__pycache__/test_gen_exports.cpython-38.pyc,,
trio/_tests/tools/__pycache__/test_mypy_annotate.cpython-38.pyc,,
trio/_tests/tools/test_gen_exports.py,sha256=IeCmWsneSTPFESQEFpwgABt2ENc3ujoiLyjg956mTEI,4936
trio/_tests/tools/test_mypy_annotate.py,sha256=K_LsBJpTHSX_9g3dCreeOwrnxh_3FLjAvU-kgqVx_yA,4114
trio/_tests/type_tests/__pycache__/check_wraps.cpython-38.pyc,,
trio/_tests/type_tests/__pycache__/open_memory_channel.cpython-38.pyc,,
trio/_tests/type_tests/__pycache__/path.cpython-38.pyc,,
trio/_tests/type_tests/__pycache__/raisesgroup.cpython-38.pyc,,
trio/_tests/type_tests/__pycache__/task_status.cpython-38.pyc,,
trio/_tests/type_tests/check_wraps.py,sha256=SN5UGLMOLDa3tR75r0i4LenYUZKmjQzDl2VfG4UdTKI,284
trio/_tests/type_tests/open_memory_channel.py,sha256=Iaiu47Crt9bE3Qk7ecigrdLCgcFdmY60Xx5d8dxhe30,107
trio/_tests/type_tests/path.py,sha256=ms7Bmz_7K3HJhsc8aJGU5UVsV1Mx-y5KkezBg2yiKg4,5991
trio/_tests/type_tests/raisesgroup.py,sha256=lf5j7MjOng2GiBzKUpMBii-3T9lw14H5vCh-eIjISoA,9747
trio/_tests/type_tests/task_status.py,sha256=t6xSkzp8EcQCABQbSDOLKXAw3cixpImT9XToNTUVHlQ,953
trio/_threads.py,sha256=pR1K3gxnEbd7qPgM4VISxwtga_LJPK07W2CMfW2lJJY,23922
trio/_timeouts.py,sha256=zT1rgR8U9gqNllfy6I3PNQ7LdC0qxENgWdF-3mdyaqQ,6108
trio/_tools/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
trio/_tools/__pycache__/__init__.cpython-38.pyc,,
trio/_tools/__pycache__/gen_exports.cpython-38.pyc,,
trio/_tools/__pycache__/mypy_annotate.cpython-38.pyc,,
trio/_tools/gen_exports.py,sha256=kuqPPUiE93i-JzJYV3d50FN8AB6kjOQWX128stqbr4Y,11914
trio/_tools/mypy_annotate.py,sha256=Sg4LKNypK5aryYq2kDE-3zhjS7M7UIJnnE3y1NwFWgs,4018
trio/_unix_pipes.py,sha256=HDmKdjL0YPd4xD7waw_ADz3YpQEVT6CYDHGF9qPu8-M,8194
trio/_util.py,sha256=sTJrTTQ2hHlyEr4q0QMh-PobU9Ckef1XW2ZKizek4_4,14476
trio/_version.py,sha256=WQzkNCIkajbxoPVyT35xZ3SPaFHTC1aHnn7zxBjkavY,90
trio/_wait_for_object.py,sha256=nhmmHHJ7ooA80uVQwNqOkhV0fSYaBzAuKI0g4FUabIw,2081
trio/_windows_pipes.py,sha256=xtJu6NhbDv9ewoXCTcIfNUuL6kEZUB47wOaFS0sx68Y,4855
trio/abc.py,sha256=Nx74h6S60QQ1_zl7EQDhRVaHK772odL1o8nPtiSc_8g,907
trio/from_thread.py,sha256=gtSlGAOkk_pk8Qh4QLNVKgAndWImvMsFjmqVs5tj2B8,442
trio/lowlevel.py,sha256=dqoU0nYSjdjEWBg81l5_qO3ct4-TJnV4v_aWwTUo2lQ,3149
trio/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
trio/socket.py,sha256=VhxxruT6PtHomyRrQh1Nz7uAMJUOKhghgWMGmSN2CnY,22932
trio/testing/__init__.py,sha256=bnFBtml5dzYAwx2oZMKusKc1OdroZ_a40lED0R6nf_c,1480
trio/testing/__pycache__/__init__.cpython-38.pyc,,
trio/testing/__pycache__/_check_streams.cpython-38.pyc,,
trio/testing/__pycache__/_checkpoints.cpython-38.pyc,,
trio/testing/__pycache__/_fake_net.cpython-38.pyc,,
trio/testing/__pycache__/_memory_streams.cpython-38.pyc,,
trio/testing/__pycache__/_network.cpython-38.pyc,,
trio/testing/__pycache__/_raises_group.cpython-38.pyc,,
trio/testing/__pycache__/_sequencer.cpython-38.pyc,,
trio/testing/__pycache__/_trio_test.cpython-38.pyc,,
trio/testing/_check_streams.py,sha256=3pC7uhUmiUO-mGMIL0ZUzzjKLQJYV39yKmYvAJ7C2gc,22789
trio/testing/_checkpoints.py,sha256=GYJcBMrrGPVq7f1ihrFG0QHH_WgECLoesySJj6bvi-U,2135
trio/testing/_fake_net.py,sha256=wixf_SWvVXzaMiA1kdal4qAZOmN8omcWb-IFaQpc_PY,17926
trio/testing/_memory_streams.py,sha256=Ul9H6H6iZIbK92Lsj8-REhgQ0umZw7gjHRWtubisJY0,23175
trio/testing/_network.py,sha256=PNlhXTtJBgqrUnwAS7x5-dZfGUCq8akXvt4xoCudldg,1171
trio/testing/_raises_group.py,sha256=peiOO_LGp54TCZq0qQqku_A-SZ_RCDIbdF1KrF0jQNM,22956
trio/testing/_sequencer.py,sha256=S2Hbxoaur6puEk31QP7WKYjPOOC1iRnz3KE2UOhiSRY,2772
trio/testing/_trio_test.py,sha256=p2nuguloIw610uzvPzWJOO9NtY-MMaxjljaX9EBud9Y,1386
trio/to_thread.py,sha256=KsbqCvSQK-y-zHYda111seDpqhxyYL14wHQ5_vYJjjs,228
