#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模板管理超级工具
===============

整合模板检查和修复功能的一站式工具

功能特性:
1. 全面检查所有模板文件
2. 自动修复常见问题
3. 生成详细报告
4. 支持备份和回滚
5. Bootstrap升级支持
"""

import sys
import json
from pathlib import Path
from datetime import datetime
import argparse

# 导入子工具
try:
    from template_super_checker import TemplateSuperChecker
    from template_issue_fixer import TemplateIssueFixer
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保 template_super_checker.py 和 template_issue_fixer.py 在同一目录")
    sys.exit(1)


class TemplateManager:
    """模板管理器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.templates_root = self.project_root / "app" / "templates"
        
        # 检查项目结构
        if not self.templates_root.exists():
            raise FileNotFoundError(f"模板目录不存在: {self.templates_root}")
        
        self.checker = TemplateSuperChecker(str(self.project_root))
        self.fixer = None  # 将在检查后初始化
        
        print("🚀 模板管理超级工具")
        print("=" * 50)
        print(f"📁 项目目录: {self.project_root}")
        print(f"📄 模板目录: {self.templates_root}")
        print("=" * 50)
    
    def run_check_only(self):
        """只运行检查"""
        print("\n🔍 运行模板检查...")
        
        # 运行检查
        results = self.checker.run_full_check()
        
        # 保存结果
        self.checker.save_results()
        
        return results
    
    def run_fix_only(self, check_results_file: str = None):
        """只运行修复"""
        print("\n🔧 运行模板修复...")
        
        # 加载检查结果
        check_results = None
        if check_results_file:
            try:
                with open(check_results_file, 'r', encoding='utf-8') as f:
                    check_results = json.load(f)
                print(f"✅ 已加载检查结果: {check_results_file}")
            except Exception as e:
                print(f"❌ 加载检查结果失败: {e}")
                return
        
        # 创建修复器
        self.fixer = TemplateIssueFixer(str(self.project_root), check_results)
        
        # 运行修复
        if check_results:
            fix_results = self.fixer.run_fixes_from_check_results()
        else:
            fix_results = self.fixer.run_fixes_all_templates()
        
        # 保存修复报告
        self.fixer.save_fix_report(fix_results)
        
        return fix_results
    
    def run_check_and_fix(self):
        """运行检查和修复"""
        print("\n🔍 第一步: 运行模板检查...")
        
        # 1. 运行检查
        check_results = self.checker.run_full_check()
        
        # 保存检查结果
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        check_file = self.project_root / f"template_check_report_{timestamp}.json"
        with open(check_file, 'w', encoding='utf-8') as f:
            json.dump(check_results, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 检查结果已保存: {check_file}")
        
        # 2. 分析检查结果
        total_issues = sum(len(file_data["issues"]) for file_data in check_results["files"].values())
        
        if total_issues == 0:
            print("\n✅ 未发现问题，无需修复")
            self.checker.save_results()
            return check_results, []
        
        print(f"\n⚠️ 发现 {total_issues} 个问题")
        
        # 询问是否继续修复
        response = input("🤔 是否继续自动修复？(y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("❌ 用户取消修复")
            self.checker.save_results()
            return check_results, []
        
        print("\n🔧 第二步: 运行自动修复...")
        
        # 3. 运行修复
        self.fixer = TemplateIssueFixer(str(self.project_root), check_results)
        fix_results = self.fixer.run_fixes_from_check_results()
        
        # 4. 保存所有报告
        self.checker.save_results()
        self.fixer.save_fix_report(fix_results)
        
        return check_results, fix_results
    
    def run_bootstrap_upgrade_check(self):
        """专门的Bootstrap升级检查"""
        print("\n🎨 运行Bootstrap升级检查...")
        
        # 运行检查
        results = self.checker.run_full_check()
        
        # 分析Bootstrap使用情况
        bootstrap_analysis = results["bootstrap_analysis"]
        
        print(f"\n📊 Bootstrap分析结果:")
        print(f"  • 检测版本: {bootstrap_analysis['version_detected'] or '未检测到'}")
        print(f"  • CSS文件: {len(set(bootstrap_analysis['css_files']))} 个")
        print(f"  • JS文件: {len(set(bootstrap_analysis['js_files']))} 个")
        
        # 检查是否需要升级
        version = bootstrap_analysis.get('version_detected', '')
        if 'Bootstrap 4' in version:
            print("\n⚠️ 检测到Bootstrap 4，建议升级到Bootstrap 5")
            print("💡 可以使用Bootstrap升级工具进行自动升级")
            
            # 统计需要升级的内容
            bs4_attributes = sum(1 for attr in bootstrap_analysis['attributes_used'] 
                               if attr.startswith('data-toggle') or attr.startswith('data-target'))
            
            if bs4_attributes > 0:
                print(f"  • 需要更新的Bootstrap 4属性: {bs4_attributes} 个")
            
            # 询问是否运行Bootstrap升级修复
            response = input("🤔 是否运行Bootstrap升级修复？(y/N): ").strip().lower()
            if response in ['y', 'yes']:
                self.run_bootstrap_upgrade_fix(results)
        
        elif 'Bootstrap 5' in version:
            print("\n✅ 已使用Bootstrap 5")
        else:
            print("\n❓ 未明确检测到Bootstrap版本")
        
        # 保存结果
        self.checker.save_results()
        
        return results
    
    def run_bootstrap_upgrade_fix(self, check_results: dict):
        """运行Bootstrap升级修复"""
        print("\n🔄 运行Bootstrap升级修复...")
        
        # 创建专门的Bootstrap修复器
        self.fixer = TemplateIssueFixer(str(self.project_root), check_results)
        
        # 运行修复
        fix_results = self.fixer.run_fixes_all_templates()
        
        # 保存修复报告
        self.fixer.save_fix_report(fix_results)
        
        print("\n✅ Bootstrap升级修复完成")
        print("💡 建议:")
        print("  1. 测试所有页面功能")
        print("  2. 检查Bootstrap 5的新特性")
        print("  3. 更新相关的CSS和JS文件")
        
        return fix_results
    
    def show_template_statistics(self):
        """显示模板统计信息"""
        print("\n📊 模板统计信息...")
        
        # 快速扫描
        template_files = list(self.templates_root.rglob("*.html"))
        
        print(f"📄 模板文件总数: {len(template_files)}")
        
        # 按目录统计
        dir_stats = {}
        for file_path in template_files:
            relative_path = file_path.relative_to(self.templates_root)
            directory = str(relative_path.parent) if relative_path.parent != Path('.') else 'root'
            dir_stats[directory] = dir_stats.get(directory, 0) + 1
        
        print("\n📂 目录分布:")
        for directory, count in sorted(dir_stats.items()):
            print(f"  • {directory}: {count} 个文件")
        
        # 文件大小统计
        total_size = sum(f.stat().st_size for f in template_files)
        print(f"\n💾 总大小: {total_size:,} 字节 ({total_size/1024:.1f} KB)")
        
        return {
            "total_files": len(template_files),
            "directory_stats": dir_stats,
            "total_size": total_size
        }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="模板管理超级工具")
    parser.add_argument("--project-root", default=".", help="项目根目录路径")
    
    # 操作模式
    mode_group = parser.add_mutually_exclusive_group(required=True)
    mode_group.add_argument("--check", action="store_true", help="只运行检查")
    mode_group.add_argument("--fix", help="只运行修复（可指定检查结果文件）")
    mode_group.add_argument("--check-and-fix", action="store_true", help="运行检查和修复")
    mode_group.add_argument("--bootstrap-check", action="store_true", help="Bootstrap升级检查")
    mode_group.add_argument("--stats", action="store_true", help="显示模板统计信息")
    
    args = parser.parse_args()
    
    try:
        # 创建模板管理器
        manager = TemplateManager(args.project_root)
        
        # 根据模式执行相应操作
        if args.check:
            manager.run_check_only()
            
        elif args.fix:
            manager.run_fix_only(args.fix if args.fix != True else None)
            
        elif args.check_and_fix:
            manager.run_check_and_fix()
            
        elif args.bootstrap_check:
            manager.run_bootstrap_upgrade_check()
            
        elif args.stats:
            manager.show_template_statistics()
        
        print("\n🎉 操作完成！")
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
