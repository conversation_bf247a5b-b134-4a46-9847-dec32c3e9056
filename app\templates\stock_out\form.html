{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ title }}</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('stock_out.index') }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" action="{{ url_for('stock_out.edit', id=stock_out.id) if stock_out else url_for('stock_out.create') }}"><div class="row">
                            <div class="col-md-6">
                                {% if not stock_out %}
                                <div class="mb-3">
                                    <label for="warehouse_id">仓库 <span class="text-danger">*</span></label>
                                    <select class="form-control" id="warehouse_id" name="warehouse_id" required>
                                        <option value="">-- 请选择仓库 --</option>
                                        {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                {% endif %}
                                
                                <div class="mb-3">
                                    <label for="stock_out_date">出库日期 <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="stock_out_date" name="stock_out_date" value="{{ stock_out.stock_out_date.strftime('%Y-%m-%d') if stock_out else '' }}" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="stock_out_type">出库类型 <span class="text-danger">*</span></label>
                                    <select class="form-control" id="stock_out_type" name="stock_out_type" required>
                                        <option value="">-- 请选择出库类型 --</option>
                                        <option value="消耗出库" {% if stock_out and stock_out.stock_out_type == '消耗出库' %}selected{% endif %}>消耗出库</option>
                                        <option value="调拨出库" {% if stock_out and stock_out.stock_out_type == '调拨出库' %}selected{% endif %}>调拨出库</option>
                                        <option value="报损出库" {% if stock_out and stock_out.stock_out_type == '报损出库' %}selected{% endif %}>报损出库</option>
                                        <option value="退货出库" {% if stock_out and stock_out.stock_out_type == '退货出库' %}selected{% endif %}>退货出库</option>
                                        <option value="其他出库" {% if stock_out and stock_out.stock_out_type == '其他出库' %}selected{% endif %}>其他出库</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="recipient">领用人</label>
                                    <input type="text" class="form-control" id="recipient" name="recipient" value="{{ stock_out.recipient if stock_out else '' }}">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="department">领用部门</label>
                                    <input type="text" class="form-control" id="department" name="department" value="{{ stock_out.department if stock_out else '' }}">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="notes">备注</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3">{{ stock_out.notes if stock_out else '' }}</textarea>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12 text-center">
                                <button type="submit" class="btn btn-primary">保存</button>
                                <a href="{{ url_for('stock_out.index') }}" class="btn btn-default">取消</a>
                            </div>
                        </div>
                    
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
