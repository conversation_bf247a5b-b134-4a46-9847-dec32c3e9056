{% extends 'base.html' %}

{% block title %}临期/过期库存检查{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">⚠️ 临期/过期库存检查</h5>
                    <div class="card-tools">
                        <a href="{{ url_for('inventory.index') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回库存列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 搜索表单 -->
                    <form method="get" action="{{ url_for('inventory.check_expiry') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label>仓库</label>
                                    <select name="warehouse_id" class="form-control">
                                        <option value="">全部</option>
                                        {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.id }}" {% if warehouse_id == warehouse.id %}selected{% endif %}>{{ warehouse.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label>临期天数</label>
                                    <select name="days" class="form-control">
                                        <option value="3" {% if days == 3 %}selected{% endif %}>3天内过期</option>
                                        <option value="7" {% if days == 7 %}selected{% endif %}>7天内过期</option>
                                        <option value="15" {% if days == 15 %}selected{% endif %}>15天内过期</option>
                                        <option value="30" {% if days == 30 %}selected{% endif %}>30天内过期</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-primary btn-block">搜索</button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- 已过期库存 -->
                    <div class="card card-danger">
                        <div class="card-header">
                            <h6 class="card-title">🚨 已过期库存</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>食材名称</th>
                                            <th>仓库</th>
                                            <th>存储位置</th>
                                            <th>批次号</th>
                                            <th>数量</th>
                                            <th>单位</th>
                                            <th>过期日期</th>
                                            <th>过期天数</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for inventory in expired_inventories %}
                                        <tr>
                                            <td>{{ inventory.ingredient.name }}</td>
                                            <td>{{ inventory.warehouse.name }}</td>
                                            <td>{{ inventory.storage_location.name }} ({{ inventory.storage_location.location_code }})</td>
                                            <td>{{ inventory.batch_number }}</td>
                                            <td>{{ inventory.quantity }}</td>
                                            <td>{{ inventory.unit }}</td>
                                            <td>{{  inventory.expiry_date|format_datetime('%Y-%m-%d')  }}</td>
                                            <td>
                                                <span class="badge badge-danger">{{ inventory.days_expired }} 天</span>
                                            </td>
                                            <td>
                                                <a href="{{ url_for('inventory.detail', id=inventory.id) }}" class="btn btn-info btn-sm">
                                                    <i class="fas fa-eye"></i> 查看
                                                </a>
                                            </td>
                                        </tr>
                                        {% else %}
                                        <tr>
                                            <td colspan="9" class="text-center">暂无已过期库存</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 临期库存 -->
                    <div class="card card-warning mt-4">
                        <div class="card-header">
                            <h6 class="card-title">⏰ 临期库存 ({{ days }}天内过期)</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>食材名称</th>
                                            <th>仓库</th>
                                            <th>存储位置</th>
                                            <th>批次号</th>
                                            <th>数量</th>
                                            <th>单位</th>
                                            <th>过期日期</th>
                                            <th>剩余天数</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for inventory in expiring_inventories %}
                                        {% if inventory.status != '已过期' %}
                                        <tr>
                                            <td>{{ inventory.ingredient.name }}</td>
                                            <td>{{ inventory.warehouse.name }}</td>
                                            <td>{{ inventory.storage_location.name }} ({{ inventory.storage_location.location_code }})</td>
                                            <td>{{ inventory.batch_number }}</td>
                                            <td>{{ inventory.quantity }}</td>
                                            <td>{{ inventory.unit }}</td>
                                            <td>{{  inventory.expiry_date|format_datetime('%Y-%m-%d')  }}</td>
                                            <td>
                                                <span class="badge {% if inventory.days_remaining <= 3 %}badge-danger{% elif inventory.days_remaining <= 7 %}badge-warning{% else %}badge-info{% endif %}">
                                                    {{ inventory.days_remaining }} 天
                                                </span>
                                            </td>
                                            <td>
                                                <a href="{{ url_for('inventory.detail', id=inventory.id) }}" class="btn btn-info btn-sm">
                                                    <i class="fas fa-eye"></i> 查看
                                                </a>
                                            </td>
                                        </tr>
                                        {% endif %}
                                        {% else %}
                                        <tr>
                                            <td colspan="9" class="text-center">暂无临期库存</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
