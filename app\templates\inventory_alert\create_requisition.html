{% extends 'base.html' %}

{% block title %}创建采购申请{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">根据库存预警创建采购申请</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('inventory_alert.view', id=inventory_alert.id) }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回预警详情
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th class="w-30">预警ID</th>
                                    <td>{{ inventory_alert.id }}</td>
                                </tr>
                                <tr>
                                    <th>区域</th>
                                    <td>{{ inventory_alert.area.name }}</td>
                                </tr>
                                <tr>
                                    <th>食材</th>
                                    <td>{{ inventory_alert.ingredient.name }}</td>
                                </tr>
                                <tr>
                                    <th>当前库存</th>
                                    <td>{{ inventory_alert.current_quantity }} {{ inventory_alert.unit }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th class="w-30">预警类型</th>
                                    <td>
                                        {% if inventory_alert.alert_type == '库存不足' %}
                                        <span class="badge badge-danger">库存不足</span>
                                        {% elif inventory_alert.alert_type == '库存过多' %}
                                        <span class="badge badge-warning">库存过多</span>
                                        {% elif inventory_alert.alert_type == '临近过期' %}
                                        <span class="badge badge-info">临近过期</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>状态</th>
                                    <td>
                                        {% if inventory_alert.status == '未处理' %}
                                        <span class="badge badge-warning">未处理</span>
                                        {% elif inventory_alert.status == '已处理' %}
                                        <span class="badge badge-success">已处理</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>创建时间</th>
                                    <td>{{ inventory_alert.created_at }}</td>
                                </tr>
                                <tr>
                                    <th>备注</th>
                                    <td>{{ inventory_alert.notes or '-' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- 创建采购申请表单 -->
                    <form method="post" action="{{ url_for('inventory_alert.create_requisition', id=inventory_alert.id) }}" class="mt-4"><div class="card">
                            <div class="card-header">
                                <h4 class="card-title">采购申请信息</h4>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="quantity">采购数量 <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <input type="number" name="quantity" id="quantity" class="form-control" min="0.1" step="0.1" required>
                                                <div class="">
                                                    <span class="input-group-text">{{ inventory_alert.unit }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="required_date">需求日期 <span class="text-danger">*</span></label>
                                            <input type="date" name="required_date" id="required_date" class="form-control" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="notes">备注</label>
                                    <textarea name="notes" id="notes" class="form-control" rows="3"></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3 text-center mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 创建采购申请
                            </button>
                            <a href="{{ url_for('inventory_alert.view', id=inventory_alert.id) }}" class="btn btn-default">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                    
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 设置默认需求日期为明天
        var tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        var tomorrowStr = tomorrow.toISOString().split('T')[0];
        $('#required_date').val(tomorrowStr);
        
        // 设置默认采购数量为当前库存的两倍
        var currentQuantity = {{ inventory_alert.current_quantity }};
        var suggestedQuantity = Math.max(1, currentQuantity * 2);
        $('#quantity').val(suggestedQuantity);
    });
</script>
{% endblock %}
