{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ title }}</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('warehouse.view', id=warehouse.id) }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回仓库详情
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" action="{{ url_for('storage_location.edit', id=storage_location.id) if storage_location else url_for('storage_location.create', warehouse_id=warehouse.id) }}" novalidate><div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name">名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" value="{{ storage_location.name if storage_location else '' }}" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="location_code">位置编码 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="location_code" name="location_code" value="{{ storage_location.location_code if storage_location else '' }}" required>
                                    <small class="form-text text-muted">位置编码应该唯一，例如：A01、B02等</small>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="storage_type">存储类型 <span class="text-danger">*</span></label>
                                    <select class="form-control" id="storage_type" name="storage_type" required>
                                        <option value="">-- 请选择存储类型 --</option>
                                        <option value="常温" {% if storage_location and storage_location.storage_type == '常温' %}selected{% endif %}>常温</option>
                                        <option value="冷藏" {% if storage_location and storage_location.storage_type == '冷藏' %}selected{% endif %}>冷藏</option>
                                        <option value="冷冻" {% if storage_location and storage_location.storage_type == '冷冻' %}selected{% endif %}>冷冻</option>
                                        <option value="干燥" {% if storage_location and storage_location.storage_type == '干燥' %}selected{% endif %}>干燥</option>
                                        <option value="其他" {% if storage_location and storage_location.storage_type == '其他' %}selected{% endif %}>其他</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="capacity">容量</label>
                                    <input type="number" class="form-control" id="capacity" name="capacity" value="{{ storage_location.capacity if storage_location else '' }}" step="0.01">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="capacity_unit">容量单位</label>
                                    <input type="text" class="form-control" id="capacity_unit" name="capacity_unit" value="{{ storage_location.capacity_unit if storage_location else '立方米' }}">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="temperature_range">温度范围</label>
                                    <input type="text" class="form-control" id="temperature_range" name="temperature_range" value="{{ storage_location.temperature_range if storage_location else '' }}" placeholder="例如：2-8°C">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="status">状态 <span class="text-danger">*</span></label>
                                    <select class="form-control" id="status" name="status" required>
                                        <option value="正常" {% if storage_location and storage_location.status == '正常' %}selected{% endif %}>正常</option>
                                        <option value="维护中" {% if storage_location and storage_location.status == '维护中' %}selected{% endif %}>维护中</option>
                                        <option value="已关闭" {% if storage_location and storage_location.status == '已关闭' %}selected{% endif %}>已关闭</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="notes">备注</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3">{{ storage_location.notes if storage_location else '' }}</textarea>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12 text-center">
                                <button type="submit" class="btn btn-primary">保存</button>
                                <a href="{{ url_for('warehouse.view', id=warehouse.id) }}" class="btn btn-default">取消</a>
                            </div>
                        </div>
                    
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
