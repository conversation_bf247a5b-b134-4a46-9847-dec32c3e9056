
{% extends 'base.html' %}

{% block title %}周菜单计划 - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('static', filename='css/save-indicator.css') }}">
<style nonce="{{ csp_nonce }}">
  /* 用户引导样式 */
  .context-guidance {
    border-start: 4px solid #17a2b8;
    background-color: #f8f9fa;
  }
  .workflow-context {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
  }
  .previous-step, .current-step, .next-step {
    flex: 1;
    padding: 0 10px;
  }
  .current-step {
    border-start: 1px solid #dee2e6;
    border-end: 1px solid #dee2e6;
  }
  .guidance-tips li {
    margin-bottom: 5px;
  }
  .step-guide-card {
    margin-bottom: 20px;
  }
  .alert-icon {
    font-size: 1.2rem;
    margin-right: 10px;
  }
  .meal-guide {
    background-color: #f8f9fa;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
  }
  .meal-type-info {
    border-start: 4px solid;
    padding-left: 15px;
    margin-bottom: 15px;
  }
  .breakfast-info {
    border-color: #28a745;
  }
  .lunch-info {
    border-color: #fd7e14;
  }
  .dinner-info {
    border-color: #6f42c1;
  }
  .nutrition-tip {
    background-color: #e8f4f8;
    border-radius: 5px;
    padding: 10px;
    margin-top: 10px;
    font-size: 0.9rem;
  }

  /* 菜单输入框样式 */
  .menu-input {
    cursor: pointer;
    background-color: #fff;
    transition: all 0.3s;
    min-height: 80px; /* 增加高度 */
    font-size: 14px; /* 调整字体大小 */
    padding: 8px 12px; /* 增加内边距 */
    resize: none; /* 禁止调整大小 */
    overflow-y: auto; /* 内容过多时显示滚动条 */
    color: #0056b3; /* 蓝色文本，更易读 */
    font-weight: 500;
  }
  .menu-input.selected {
    border-color: #4CAF50;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.3);
  }

  /* 表格样式调整 */
  .table th, .table td {
    vertical-align: middle;
    text-align: center;
    padding: 12px; /* 增加单元格内边距 */
  }

  /* 日期列样式 */
  .date-column {
    width: 15%;
    background-color: #f8f9fa;
    font-weight: bold;
  }

  /* 模态框样式 */
  .modal {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1055;
    display: none;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    outline: 0;
  }

  .modal.show {
    display: block;
  }

  .modal-dialog {
    position: relative;
    width: auto;
    margin: 0.5rem;
    pointer-events: none;
    max-width: 800px;
    margin: 1.75rem auto;
  }

  .modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 0.3rem;
    outline: 0;
  }

  .modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1050;
    width: 100vw;
    height: 100vh;
    background-color: #000;
  }

  .modal-backdrop.fade {
    opacity: 0;
    transition: opacity 0.15s linear;
  }

  .modal-backdrop.show {
    opacity: 0.5;
    display: block;
  }

  /* 食谱卡片样式 */
  .recipe-card {
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .recipe-card:hover {
    border-color: #6c757d;
    background-color: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .recipe-card.selected {
    border-color: #28a745;
    background-color: #e8f4e8;
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.5);
  }

  /* 已选食谱标签样式 */
  .selected-recipe-tag {
    display: inline-flex;
    align-items: center;
    background-color: #e8f4e8;
    color: #28a745;
    border: 1px solid #28a745;
    border-radius: 0.25rem;
    padding: 0.25rem 0.5rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .selected-recipe-tag .remove-btn {
    margin-left: 0.5rem;
    cursor: pointer;
    color: #dc3545;
  }

  /* 菜单单元格样式 */
  .menu-cell {
    cursor: pointer;
    min-height: 80px;
    transition: all 0.2s ease;
  }

  .menu-cell:hover {
    background-color: #f8f9fa;
  }

  .menu-cell.selected {
    background-color: #e8f4e8;
    border: 1px solid #28a745;
  }

  /* 自定义食谱输入框样式 */
  #customRecipeInput {
    border: 1px solid #ced4da;
  }

  #customRecipeInput:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
  }

  /* 食谱名称显示样式 */
  .recipe-name {
    color: #dc3545; /* 红色文本 */
    font-weight: 500;
  }

  /* 不同餐次的颜色区分 */
  .breakfast {
    border-start: 4px solid #28a745; /* 早餐绿色 */
  }
  .lunch {
    border-start: 4px solid #fd7e14; /* 午餐橙色 */
  }
  .dinner {
    border-start: 4px solid #6f42c1; /* 晚餐紫色 */
  }

  /* 食谱分类样式 */
  .list-group-item-divider {
    height: 1px;
    background-color: #dee2e6;
    margin: 0.5rem 0;
  }

  .list-group-item-header {
    padding: 0.5rem 1rem;
    font-weight: 600;
    background-color: #f8f9fa;
    border: none;
    font-size: 0.875rem;
  }

  .recipe-categories .list-group-item {
    border-start: 3px solid transparent;
    transition: all 0.2s ease;
    padding: 0.75rem 1rem;
  }

  .recipe-categories .list-group-item:hover {
    background-color: #f8f9fa;
    border-start-color: #007bff;
  }

  .recipe-categories .list-group-item.active {
    background-color: #e3f2fd;
    border-start-color: #2196f3;
    color: #1976d2;
  }

  .recipe-categories .list-group-item i {
    margin-right: 0.5rem;
    width: 1rem;
  }

  /* 兼容性处理 */
  @supports not (display: flex) {
    .d-flex {
      display: block;
    }

    .flex-wrap {
      display: block;
    }

    .selected-recipe-tag {
      display: inline-block;
    }
  }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
  <!-- 周菜单计划流程引导 -->
  <div class="context-guidance card mb-4 border-primary">
    <div class="card-header bg-primary text-white">
      <h5 class="mb-0"><i class="fas fa-calendar-alt"></i> 周菜单计划 - 流程指引</h5>
    </div>
    <div class="card-body">
      <div class="alert alert-info">
        <i class="fas fa-info-circle alert-icon"></i> <strong>提示：</strong> 周菜单计划是食堂管理的第一步，合理的菜单安排将影响后续的采购、入库和消耗计划。
      </div>

      <div class="workflow-context mt-3">
        <div class="previous-step">
          <small class="text-muted">上一步</small>
          <p><i class="fas fa-list"></i> 查看历史菜单</p>
          <small>参考历史菜单进行规划</small>
          <div class="mt-2">
            <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-sm btn-outline-secondary">
              <i class="fas fa-arrow-left"></i> 查看历史菜单
            </a>
          </div>
        </div>
        <div class="current-step bg-light p-2 border rounded">
          <small class="text-muted">当前步骤</small>
          <p class="fw-bold"><i class="fas fa-calendar-alt"></i> 制定周菜单计划</p>
          <small>为一周的各餐次安排菜品</small>
        </div>
        <div class="next-step">
          <small class="text-muted">下一步</small>
          <p><i class="fas fa-shopping-cart"></i> 生成采购计划</p>
          <small>根据菜单自动生成采购清单</small>
          <div class="mt-2">
            <button type="button" class="btn btn-sm btn-outline-primary go-to-purchase-plan">
              <i class="fas fa-arrow-right"></i> 前往采购计划
            </button>
          </div>
        </div>
      </div>

      <!-- 餐次指南 -->
      <div class="meal-guide mt-4">
        <h6 class="fw-bold"><i class="fas fa-utensils"></i> 餐次规划指南</h6>
        <div class="row">
          <div class="col-md-4">
            <div class="meal-type-info breakfast-info">
              <h6 class="text-success">早餐</h6>
              <ul class="small mb-0">
                <li>包含主食、蛋白质和水果</li>
                <li>推荐：粥、包子、鸡蛋、牛奶</li>
                <li>避免过于油腻的食物</li>
              </ul>
            </div>
          </div>
          <div class="col-md-4">
            <div class="meal-type-info lunch-info">
              <h6 class="text-warning">午餐</h6>
              <ul class="small mb-0">
                <li>营养丰富，荤素搭配</li>
                <li>推荐：米饭/面食、肉类、蔬菜</li>
                <li>注意食材多样性</li>
              </ul>
            </div>
          </div>
          <div class="col-md-4">
            <div class="meal-type-info dinner-info">
              <h6 class="text-purple">晚餐</h6>
              <ul class="small mb-0">
                <li>适量且易消化</li>
                <li>推荐：清淡食物、蔬菜为主</li>
                <li>避免过于油腻和刺激性食物</li>
              </ul>
            </div>
          </div>
        </div>
        <div class="nutrition-tip">
          <p class="mb-0"><i class="fas fa-lightbulb text-warning"></i> <strong>营养均衡提示：</strong> 一周菜单应包含多种食材类型，确保蛋白质、碳水化合物、蔬菜水果、奶制品等均衡搭配。避免连续几天提供相似的菜品。</p>
        </div>
      </div>

      <!-- 操作指南 -->
      <div class="alert alert-light border">
        <h6 class="alert-heading"><i class="fas fa-mouse-pointer"></i> 操作指南</h6>
        <ol class="mb-0">
          <li>点击表格中的单元格（对应某天的某餐次）</li>
          <li>在弹出的窗口中选择或搜索菜品</li>
          <li>可以选择多个菜品，也可以添加自定义菜品</li>
          <li>点击"保存"按钮确认选择</li>
          <li>完成所有餐次的菜品选择后，点击页面底部的"保存周菜单"按钮</li>
        </ol>
      </div>
    </div>
  </div>

  <div class="row mb-4">
    <div class="col-md-8">
      <h2>{{ area.name }}周菜单计划</h2>
      <p class="text-muted">{{ week_start }} 至 {{ week_end }}</p>
    </div>
    <div class="col-md-4 text-end">
      <a href="{{ url_for('weekly_menu.plan') }}" class="btn btn-success me-2">
        <i class="fas fa-plus"></i> 新建菜单
      </a>
      <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> 返回列表
      </a>
    </div>
  </div>

  <div class="row">
    <div class="col-md-12">
      <div class="card">
        <div class="card-body">
          <form id="menuForm" method="post">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            <input type="hidden" name="menu_data" id="menuData" value="">
            <input type="hidden" name="area_id" id="area_id" value="{{ area.id }}">

            <table class="table table-bordered">
              <thead>
                <tr>
                  <th class="w-15">日期</th>
                  <th>早餐</th>
                  <th>午餐</th>
                  <th>晚餐</th>
                </tr>
              </thead>
              <tbody>
                {% for date_str, day_data in week_dates.items() %}
                <tr>
                  <td class="date-column">
                    <div class="fw-bold">{{ day_data.weekday }}</div>
                    <div>{{ date_str }}</div>
                  </td>
                  {% set meal_classes = {'早餐': 'breakfast', '午餐': 'lunch', '晚餐': 'dinner'} %}
                  {% for meal_type in ['早餐', '午餐', '晚餐'] %}
                  <td>
                    <textarea
                           class="form-control menu-input {{ meal_classes[meal_type] }}"
                           data-date="{{ date_str }}"
                           data-meal="{{ meal_type }}"
                           placeholder="点击选择菜谱"
                           readonly
                           rows="3">{{ menu_data[date_str][meal_type]|join(', ') if menu_data.get(date_str, {}).get(meal_type) }}</textarea>
                  </td>
                  {% endfor %}
                </tr>
                {% endfor %}
              </tbody>
            </table>

            <div class="text-center mt-4">
              <button type="submit" class="btn btn-primary btn-lg">
                <i class="fas fa-save"></i> 保存周菜单
              </button>
              <!-- 复制功能已移除 -->
              <!-- 不再需要清除缓存按钮 -->
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- 保存后操作指引 -->
  <div class="card mt-4 mb-4 border-success">
    <div class="card-header bg-success text-white">
      <h5 class="mb-0"><i class="fas fa-check-circle"></i> 完成菜单计划后的后续操作</h5>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-6">
          <div class="card h-100">
            <div class="card-header bg-info text-white">
              <h6 class="mb-0"><i class="fas fa-shopping-cart"></i> 生成采购计划</h6>
            </div>
            <div class="card-body">
              <p>根据周菜单自动生成采购计划，确保所需食材及时采购。</p>
              <ul>
                <li>系统会根据菜谱自动计算所需食材数量</li>
                <li>可以根据实际情况调整采购数量</li>
                <li>采购计划可导出为PDF格式打印</li>
              </ul>
              <div class="text-center mt-3">
                <button type="button" class="btn btn-info go-to-purchase-plan">
                  <i class="fas fa-shopping-cart"></i> 生成采购计划
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="card h-100">
            <div class="card-header bg-primary text-white">
              <h6 class="mb-0"><i class="fas fa-print"></i> 打印周菜单</h6>
            </div>
            <div class="card-body">
              <p>打印周菜单用于公示和厨房参考。</p>
              <ul>
                <li>可以选择不同的打印格式（详细版/简洁版）</li>
                <li>打印的菜单可张贴在食堂公告栏</li>
                <li>可以导出为PDF格式分享给相关人员</li>
              </ul>
              <div class="text-center mt-3">
                {% if existing_menu %}
                <a href="{{ url_for('weekly_menu.print_menu', id=existing_menu.id, area_id=existing_menu.area_id) }}" class="btn btn-primary" target="_blank">
                  <i class="fas fa-print"></i> 打印周菜单
                </a>
                {% else %}
                <button class="btn btn-primary" disabled title="请先保存菜单才能打印">
                  <i class="fas fa-print"></i> 打印周菜单
                </button>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="alert alert-light border mt-4">
        <div class="d-flex">
          <div class="me-3">
            <i class="fas fa-lightbulb text-warning fa-2x"></i>
          </div>
          <div>
            <h6 class="alert-heading">工作流程提示</h6>
            <p>完成周菜单计划后，建议按照以下顺序进行后续操作：</p>
            <ol>
              <li>打印周菜单并公示</li>
              <li>生成采购计划</li>
              <li>联系供应商进行采购</li>
              <li>安排食材入库检查</li>
              <li>制定每日消耗计划</li>
            </ol>
            <p class="mb-0 text-success"><i class="fas fa-check-circle"></i> 提前规划可以确保食堂运营顺畅，避免食材短缺或浪费。</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 食谱选择模态框 -->
<div class="modal fade" id="recipeModal" tabindex="-1" aria-labelledby="recipeModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="recipeModalLabel">选择食谱 <span id="modal-date-meal"></span></h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
      </div>
      <div class="modal-body">
        <div class="row">
          <!-- 左侧：食谱分类 -->
          <div class="col-md-3">
            <div class="list-group recipe-categories">
              <a href="#" class="list-group-item list-group-item-action active" data-category="all">全部食谱</a>
              <a href="#" class="list-group-item list-group-item-action" data-category="favorites">收藏食谱</a>
              <div class="list-group-item-divider"></div>
              <div class="list-group-item-header text-muted small">按来源筛选</div>
              <a href="#" class="list-group-item list-group-item-action" data-source="school">
                <i class="fas fa-school text-success"></i> {{ current_area.name if current_area else '学校' }}食谱
              </a>
              <a href="#" class="list-group-item list-group-item-action" data-source="system">
                <i class="fas fa-globe text-info"></i> 系统食谱
              </a>
              <div class="list-group-item-divider"></div>
              <div class="list-group-item-header text-muted small">按分类筛选</div>
              <!-- 动态加载其他分类 -->
            </div>

            <!-- 自定义食谱输入 -->
            <div class="mt-3">
              <div class="card">
                <div class="card-header">自定义食谱</div>
                <div class="card-body">
                  <div class="mb-2">
                    <input type="text" class="form-control" id="customRecipeInput" placeholder="输入食谱名称">
                  </div>
                  <button class="btn btn-sm btn-success w-100" id="addCustomRecipeBtn">添加</button>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧：食谱列表 -->
          <div class="col-md-9">
            <!-- 搜索框 -->
            <div class="input-group mb-3">
              <input type="text" class="form-control" id="recipeSearchInput" placeholder="搜索食谱...">
              <button class="btn btn-outline-secondary" type="button" id="recipeSearchBtn">
                <i class="fas fa-search"></i>
              </button>
            </div>

            <!-- 食谱列表 -->
            <div class="recipe-list-container">
              <div class="row" id="recipeList">
                <!-- 动态加载食谱卡片 -->
              </div>
              <!-- 分页控件 -->
              <div class="pagination-container mt-3 d-flex justify-content-center">
                <nav aria-label="食谱分页">
                  <ul class="pagination" id="recipePagination">
                    <!-- 动态加载分页按钮 -->
                  </ul>
                </nav>
              </div>
            </div>

            <!-- 已选食谱 -->
            <div class="selected-recipes mt-3">
              <h6>已选食谱：</h6>
              <div id="selectedRecipesList" class="d-flex flex-wrap gap-2">
                <!-- 动态加载已选食谱 -->
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" id="saveRecipesBtn">保存</button>
      </div>
    </div>
  </div>
</div>

<!-- 模态框背景遮罩 -->
<div class="modal-backdrop fade" id="modalBackdrop" style="display: none;"></div>


{% endblock %}


{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
// 处理从周菜单创建采购计划按钮
$(document).ready(function() {
  // 点击按钮时直接跳转到采购计划创建页面
  $('.go-to-purchase-plan').click(function() {
    // 如果有菜单ID，则带上菜单ID参数
    {% if existing_menu %}
    window.location.href = "{{ url_for('purchase_order.create_from_menu') }}?menu_id={{ existing_menu.id }}";
    {% else %}
    // 如果没有菜单ID，则直接跳转，后端会使用当前用户的区域ID并获取最新的菜单
    window.location.href = "{{ url_for('purchase_order.create_from_menu') }}";
    {% endif %}
  });
});
/**
 * 周菜单计划模态框管理
 * 提供完整的模态框功能，支持多次打开，浏览器兼容性好
 */

// 全局变量
let currentInput = null;  // 当前选中的输入框
let selectedRecipes = new Map();  // 已选食谱 Map(id => {id, name})
let allRecipes = [];  // 所有食谱
let recipeCategories = [];  // 食谱分类
let customRecipeCounter = 0;  // 自定义食谱计数器

// 初始化菜单数据
window.menuData = window.menuData || {};

// 初始化函数
document.addEventListener('DOMContentLoaded', function() {
  // 初始化模态框
  initModal();

  // 绑定事件
  bindEvents();

  // 加载食谱数据
  loadRecipeData();
});

/**
 * 初始化模态框
 */
function initModal() {
  // 检查是否已加载Bootstrap
  if (typeof bootstrap === 'undefined') {
    console.warn('Bootstrap未加载，使用自定义模态框实现');
    // 添加自定义模态框实现
    window.bootstrap = {
      Modal: function(element) {
        this.element = element;

        this.show = function() {
          this.element.classList.add('show');
          document.getElementById('modalBackdrop').classList.add('show');
          document.getElementById('modalBackdrop').style.display = 'block';
          document.body.classList.add('modal-open');
          document.body.style.overflow = 'hidden';
          document.body.style.paddingRight = '15px';
        };

        this.hide = function() {
          this.element.classList.remove('show');
          document.getElementById('modalBackdrop').classList.remove('show');
          document.getElementById('modalBackdrop').style.display = 'none';
          document.body.classList.remove('modal-open');
          document.body.style.overflow = '';
          document.body.style.paddingRight = '';
        };
      }
    };
  }
}

/**
 * 绑定事件
 */
function bindEvents() {
  // 菜单输入框点击事件
  document.querySelectorAll('.menu-input').forEach(input => {
    input.addEventListener('click', function() {
      // 移除其他输入框的选中状态
      document.querySelectorAll('.menu-input').forEach(i => i.classList.remove('selected'));
      // 添加当前输入框的选中状态
      this.classList.add('selected');
      // 显示模态框
      showModal(this);
    });
  });

  // 模态框关闭按钮
  document.querySelectorAll('[data-bs-dismiss="modal"]').forEach(button => {
    button.addEventListener('click', function() {
      closeModal();
    });
  });

  // 保存按钮
  document.getElementById('saveRecipesBtn').addEventListener('click', function() {
    saveSelectedRecipes();
  });

  // 添加自定义食谱按钮
  document.getElementById('addCustomRecipeBtn').addEventListener('click', function() {
    addCustomRecipe();
  });

  // 自定义食谱输入框回车事件
  document.getElementById('customRecipeInput').addEventListener('keydown', function(e) {
    // 禁用回车键提交，根据您的记忆要求
    if (e.key === 'Enter') {
      e.preventDefault();
      addCustomRecipe();
    }
  });

  // 食谱搜索按钮
  document.getElementById('recipeSearchBtn').addEventListener('click', function() {
    searchRecipes();
  });

  // 食谱搜索输入框回车事件
  document.getElementById('recipeSearchInput').addEventListener('keydown', function(e) {
    // 禁用回车键提交，根据您的记忆要求
    if (e.key === 'Enter') {
      e.preventDefault();
      searchRecipes();
    }
  });

  // 分类点击事件委托
  document.querySelector('.recipe-categories').addEventListener('click', function(e) {
    if (e.target.classList.contains('list-group-item')) {
      e.preventDefault();
      selectCategoryOrSource(e.target);
    }
  });

  // 食谱列表点击事件委托
  document.getElementById('recipeList').addEventListener('click', function(e) {
    const recipeCard = e.target.closest('.recipe-card');
    if (recipeCard) {
      toggleRecipeSelection(recipeCard);
    }
  });

  // 已选食谱列表点击事件委托
  document.getElementById('selectedRecipesList').addEventListener('click', function(e) {
    if (e.target.classList.contains('remove-btn')) {
      const recipeId = e.target.closest('.selected-recipe-tag').dataset.id;
      removeSelectedRecipe(recipeId);
    }
  });

  // ESC键关闭模态框
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && document.querySelector('.modal.show')) {
      closeModal();
    }
  });

  // 点击模态框背景关闭模态框
  document.getElementById('modalBackdrop').addEventListener('click', function() {
    closeModal();
  });

  // 阻止模态框内容点击事件冒泡
  document.querySelector('.modal-content').addEventListener('click', function(e) {
    e.stopPropagation();
  });

  // 表单提交处理
  document.getElementById('menuForm').addEventListener('submit', function(e) {
    e.preventDefault();

    // 同步最新数据
    document.getElementById('menuData').value = JSON.stringify(window.menuData);

    // 提交表单
    this.submit();
  });
}

/**
 * 关闭模态框
 */
function closeModal() {
  const modalElement = document.getElementById('recipeModal');
  if (modalElement._bsModal) {
    modalElement._bsModal.hide();
  } else {
    // 兼容性处理
    modalElement.classList.remove('show');
    document.getElementById('modalBackdrop').classList.remove('show');
    document.getElementById('modalBackdrop').style.display = 'none';
    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';
  }

  // 清空当前选中状态
  currentInput = null;
}

/**
 * 加载输入框已有的食谱
 * @param {string} date - 日期
 * @param {string} meal - 餐次
 */
function loadInputRecipes(date, meal) {
  // 从全局菜单数据中获取
  if (window.menuData && window.menuData[date] && window.menuData[date][meal]) {
    const recipes = window.menuData[date][meal];
    recipes.forEach(recipe => {
      selectedRecipes.set(recipe.id, {
        id: recipe.id,
        name: recipe.name
      });
    });
  }
}

// 分页配置
const RECIPES_PER_PAGE = 9; // 每页显示9个食谱（3行3列）
let currentPage = 1;
let totalPages = 1;
let currentFilteredRecipes = [];

/**
 * 更新食谱列表显示
 * @param {string} [category='all'] - 分类
 * @param {string} [searchTerm=''] - 搜索词
 * @param {number} [page=1] - 页码
 * @param {string} [source=''] - 来源筛选
 */
function updateRecipeList(category = 'all', searchTerm = '', page = 1, source = '') {
  const recipeListElement = document.getElementById('recipeList');
  recipeListElement.innerHTML = '';

  // 过滤食谱
  let filteredRecipes = allRecipes;

  // 按来源过滤
  if (source) {
    if (source === 'school') {
      // 学校食谱：area_id匹配且不是全局食谱
      filteredRecipes = filteredRecipes.filter(recipe =>
        recipe.area_id && !recipe.is_global
      );
    } else if (source === 'system') {
      // 系统食谱：全局食谱
      filteredRecipes = filteredRecipes.filter(recipe =>
        recipe.is_global
      );
    }
  }

  // 按分类过滤
  if (category !== 'all') {
    if (category === 'favorites') {
      // 收藏食谱
      filteredRecipes = filteredRecipes.filter(recipe => recipe.favorite);
    } else {
      // 其他分类
      filteredRecipes = filteredRecipes.filter(recipe => recipe.category === category);
    }
  }

  // 按搜索词过滤
  if (searchTerm) {
    filteredRecipes = filteredRecipes.filter(recipe =>
      recipe.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }

  // 保存当前过滤后的食谱列表
  currentFilteredRecipes = filteredRecipes;

  // 计算总页数
  totalPages = Math.ceil(filteredRecipes.length / RECIPES_PER_PAGE);

  // 确保当前页码有效
  currentPage = page;
  if (currentPage < 1) currentPage = 1;
  if (currentPage > totalPages) currentPage = totalPages || 1;

  // 获取当前页的食谱
  const startIndex = (currentPage - 1) * RECIPES_PER_PAGE;
  const endIndex = Math.min(startIndex + RECIPES_PER_PAGE, filteredRecipes.length);
  const currentPageRecipes = filteredRecipes.slice(startIndex, endIndex);

  // 创建食谱卡片
  currentPageRecipes.forEach(recipe => {
    const isSelected = selectedRecipes.has(recipe.id);
    const card = document.createElement('div');
    card.className = `col-md-4 mb-3`;
    card.innerHTML = `
      <div class="recipe-card ${isSelected ? 'selected' : ''}" data-id="${recipe.id}">
        <div class="recipe-name">${recipe.name}</div>
        <div class="recipe-category text-muted small">${recipe.category || '未分类'}</div>
      </div>
    `;
    recipeListElement.appendChild(card);
  });

  // 如果没有食谱，显示提示
  if (filteredRecipes.length === 0) {
    recipeListElement.innerHTML = '<div class="col-12 text-center py-3">没有找到符合条件的食谱</div>';
  }

  // 更新分页控件
  updatePagination(category, searchTerm);
}

/**
 * 更新分页控件
 * @param {string} category - 当前分类
 * @param {string} searchTerm - 当前搜索词
 */
function updatePagination(category, searchTerm) {
  const paginationElement = document.getElementById('recipePagination');
  paginationElement.innerHTML = '';

  // 如果只有一页，不显示分页控件
  if (totalPages <= 1) {
    return;
  }

  // 上一页按钮
  const prevLi = document.createElement('li');
  prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
  prevLi.innerHTML = `
    <a class="page-link" href="#" aria-label="上一页" ${currentPage === 1 ? 'tabindex="-1" aria-disabled="true"' : ''}>
      <span aria-hidden="true">&laquo;</span>
    </a>
  `;
  if (currentPage > 1) {
    prevLi.addEventListener('click', function(e) {
      e.preventDefault();
      updateRecipeList(category, searchTerm, currentPage - 1);
    });
  }
  paginationElement.appendChild(prevLi);

  // 页码按钮
  // 显示当前页附近的页码
  const maxVisiblePages = 5; // 最多显示5个页码
  let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
  let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

  // 调整startPage，确保显示maxVisiblePages个页码
  if (endPage - startPage + 1 < maxVisiblePages) {
    startPage = Math.max(1, endPage - maxVisiblePages + 1);
  }

  // 第一页
  if (startPage > 1) {
    const firstLi = document.createElement('li');
    firstLi.className = 'page-item';
    firstLi.innerHTML = '<a class="page-link" href="#">1</a>';
    firstLi.addEventListener('click', function(e) {
      e.preventDefault();
      updateRecipeList(category, searchTerm, 1);
    });
    paginationElement.appendChild(firstLi);

    // 省略号
    if (startPage > 2) {
      const ellipsisLi = document.createElement('li');
      ellipsisLi.className = 'page-item disabled';
      ellipsisLi.innerHTML = '<a class="page-link" href="#" tabindex="-1" aria-disabled="true">...</a>';
      paginationElement.appendChild(ellipsisLi);
    }
  }

  // 页码
  for (let i = startPage; i <= endPage; i++) {
    const pageLi = document.createElement('li');
    pageLi.className = `page-item ${i === currentPage ? 'active' : ''}`;
    pageLi.innerHTML = `<a class="page-link" href="#">${i}</a>`;
    if (i !== currentPage) {
      pageLi.addEventListener('click', function(e) {
        e.preventDefault();
        updateRecipeList(category, searchTerm, i);
      });
    }
    paginationElement.appendChild(pageLi);
  }

  // 最后一页
  if (endPage < totalPages) {
    // 省略号
    if (endPage < totalPages - 1) {
      const ellipsisLi = document.createElement('li');
      ellipsisLi.className = 'page-item disabled';
      ellipsisLi.innerHTML = '<a class="page-link" href="#" tabindex="-1" aria-disabled="true">...</a>';
      paginationElement.appendChild(ellipsisLi);
    }

    const lastLi = document.createElement('li');
    lastLi.className = 'page-item';
    lastLi.innerHTML = `<a class="page-link" href="#">${totalPages}</a>`;
    lastLi.addEventListener('click', function(e) {
      e.preventDefault();
      updateRecipeList(category, searchTerm, totalPages);
    });
    paginationElement.appendChild(lastLi);
  }

  // 下一页按钮
  const nextLi = document.createElement('li');
  nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
  nextLi.innerHTML = `
    <a class="page-link" href="#" aria-label="下一页" ${currentPage === totalPages ? 'tabindex="-1" aria-disabled="true"' : ''}>
      <span aria-hidden="true">&raquo;</span>
    </a>
  `;
  if (currentPage < totalPages) {
    nextLi.addEventListener('click', function(e) {
      e.preventDefault();
      updateRecipeList(category, searchTerm, currentPage + 1);
    });
  }
  paginationElement.appendChild(nextLi);
}

/**
 * 更新已选食谱列表显示
 */
function updateSelectedRecipesList() {
  const selectedRecipesElement = document.getElementById('selectedRecipesList');
  selectedRecipesElement.innerHTML = '';

  if (selectedRecipes.size === 0) {
    selectedRecipesElement.innerHTML = '<div class="text-muted">未选择食谱</div>';
    return;
  }

  // 创建已选食谱标签
  selectedRecipes.forEach(recipe => {
    const tag = document.createElement('div');
    tag.className = 'selected-recipe-tag';
    tag.dataset.id = recipe.id;
    tag.innerHTML = `
      <span class="recipe-name">${recipe.name}</span>
      <span class="remove-btn">&times;</span>
    `;
    selectedRecipesElement.appendChild(tag);
  });
}

/**
 * 切换食谱选择状态
 * @param {HTMLElement} recipeCard - 食谱卡片元素
 */
function toggleRecipeSelection(recipeCard) {
  const recipeId = recipeCard.dataset.id;
  const recipeName = recipeCard.querySelector('.recipe-name').textContent;

  if (selectedRecipes.has(recipeId)) {
    // 取消选择
    selectedRecipes.delete(recipeId);
    recipeCard.classList.remove('selected');
  } else {
    // 选择
    selectedRecipes.set(recipeId, {
      id: recipeId,
      name: recipeName
    });
    recipeCard.classList.add('selected');
  }

  // 更新已选食谱列表
  updateSelectedRecipesList();
}

/**
 * 移除已选食谱
 * @param {string} recipeId - 食谱ID
 */
function removeSelectedRecipe(recipeId) {
  // 从已选食谱中移除
  selectedRecipes.delete(recipeId);

  // 更新食谱卡片状态
  const recipeCard = document.querySelector(`.recipe-card[data-id="${recipeId}"]`);
  if (recipeCard) {
    recipeCard.classList.remove('selected');
  }

  // 更新已选食谱列表
  updateSelectedRecipesList();
}

/**
 * 选择分类或来源
 * @param {HTMLElement} element - 分类或来源元素
 */
function selectCategoryOrSource(element) {
  // 移除其他分类的选中状态
  document.querySelectorAll('.recipe-categories .list-group-item').forEach(item => {
    item.classList.remove('active');
  });

  // 设置当前元素为选中状态
  element.classList.add('active');

  // 获取筛选参数
  const category = element.dataset.category || 'all';
  const source = element.dataset.source || '';
  const searchTerm = document.getElementById('recipeSearchInput').value.trim();

  // 更新食谱列表显示（重置为第1页）
  updateRecipeList(category, searchTerm, 1, source);
}

/**
 * 选择分类（保持向后兼容）
 * @param {HTMLElement} categoryElement - 分类元素
 */
function selectCategory(categoryElement) {
  selectCategoryOrSource(categoryElement);
}


/**
 * 搜索食谱
 */
function searchRecipes() {
  const searchInput = document.getElementById('recipeSearchInput');
  const searchTerm = searchInput.value.trim();

  // 获取当前选中的分类
  const activeCategory = document.querySelector('.recipe-categories .list-group-item.active');
  const category = activeCategory ? activeCategory.dataset.category : 'all';

  // 更新食谱列表显示（重置为第1页）
  updateRecipeList(category, searchTerm, 1);
}

/**
 * 添加自定义食谱
 */
function addCustomRecipe() {
  const input = document.getElementById('customRecipeInput');
  const name = input.value.trim();

  if (!name) {
    alert('请输入食谱名称');
    return;
  }

  const newRecipe = {
    id: `custom_${Date.now()}_${customRecipeCounter++}`,
    name: name,
    category: '自定义食谱',
    favorite: false
  };

  // 添加到已选食谱
  selectedRecipes.set(newRecipe.id, newRecipe);

  // 更新已选食谱列表
  updateSelectedRecipesList();

  // 清空输入框
  input.value = '';
  input.focus();
}


/**
 * 保存选择
 */
function saveSelectedRecipes() {
  if (!currentInput) {
    console.warn('没有选中的输入框');
    return;
  }

  const date = currentInput.dataset.date;
  const meal = currentInput.dataset.meal;

  // 确保menuData中有对应的日期和餐次
  if (!window.menuData[date]) {
    window.menuData[date] = {};
  }

  // 保存选择的食谱
  window.menuData[date][meal] = Array.from(selectedRecipes.values());

  // 更新输入框显示
  currentInput.value = Array.from(selectedRecipes.values())
    .map(recipe => recipe.name)
    .join(', ');

  // 保存到本地存储
  localStorage.setItem('weeklyMenu', JSON.stringify(window.menuData));

  // 关闭模态框
  closeModal();
}

/**
 * 加载食谱数据
 */
function loadRecipeData() {
  // 从后端获取的食谱数据
  allRecipes = [];

  // 处理食谱分类
  {% for category, recipes in recipes_by_category.items() %}
  recipeCategories.push("{{ category }}");
  {% for recipe in recipes %}
  allRecipes.push({
    id: "{{ recipe.id }}",
    name: "{{ recipe.name }}",
    category: "{{ category }}",
    favorite: false
  });
  {% endfor %}
  {% endfor %}

  // 初始化分类列表
  const categoriesContainer = document.querySelector('.recipe-categories');
  categoriesContainer.innerHTML = '<a href="#" class="list-group-item list-group-item-action active" data-category="all">全部食谱</a>';

  // 添加分类
  recipeCategories.forEach(category => {
    const categoryItem = document.createElement('a');
    categoryItem.href = '#';
    categoryItem.className = 'list-group-item list-group-item-action';
    categoryItem.dataset.category = category;
    categoryItem.textContent = category;
    categoriesContainer.appendChild(categoryItem);
  });

  // 初始化食谱列表
  updateRecipeList();

  // 从localStorage加载菜单数据
  if (localStorage.getItem('weeklyMenu')) {
    try {
      const localData = JSON.parse(localStorage.getItem('weeklyMenu'));
      Object.assign(window.menuData, localData);

      // 更新输入框显示
      document.querySelectorAll('.menu-input').forEach(input => {
        const date = input.dataset.date;
        const meal = input.dataset.meal;

        if (window.menuData[date] && window.menuData[date][meal]) {
          const recipes = window.menuData[date][meal];
          input.value = recipes.map(r => r.name).join(', ');
        }
      });
    } catch (e) {
      console.error('加载菜单数据失败:', e);
    }
  }
}

// 显示模态框
function showModal(input) {
  // 保存当前输入框引用
  currentInput = input;

  // 获取日期和餐次
  const date = input.dataset.date;
  const meal = input.dataset.meal;

  // 初始化已选菜品
  selectedRecipes.clear();

  // 加载当前输入框已有的食谱
  loadInputRecipes(date, meal);

  // 更新模态框显示
  document.getElementById('modal-date-meal').textContent = `${date} ${meal}`;
  updateSelectedRecipesList();
  updateRecipeList();

  // 显示模态框
  const modalElement = document.getElementById('recipeModal');
  const modal = new bootstrap.Modal(modalElement);
  modal.show();

  // 保存模态框实例到元素上，方便后续使用
  modalElement._bsModal = modal;
}
</script>
{% endblock %}


