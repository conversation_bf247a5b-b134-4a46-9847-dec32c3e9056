{% extends 'base.html' %}

{% block title %}存储位置详情{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">存储位置详情</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('warehouse.view', id=warehouse.id) }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回仓库详情
                        </a>
                        <a href="{{ url_for('storage_location.edit', id=storage_location.id) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit"></i> 编辑
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th class="w-30">名称</th>
                                    <td>{{ storage_location.name }}</td>
                                </tr>
                                <tr>
                                    <th>位置编码</th>
                                    <td>{{ storage_location.location_code }}</td>
                                </tr>
                                <tr>
                                    <th>所属仓库</th>
                                    <td>{{ warehouse.name }}</td>
                                </tr>
                                <tr>
                                    <th>存储类型</th>
                                    <td>{{ storage_location.storage_type }}</td>
                                </tr>
                                <tr>
                                    <th>创建时间</th>
                                    <td>{{  storage_location.created_at|format_datetime('%Y-%m-%d %H:%M:%S')  }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th class="w-30">容量</th>
                                    <td>{{ storage_location.capacity }} {{ storage_location.capacity_unit }}</td>
                                </tr>
                                <tr>
                                    <th>温度范围</th>
                                    <td>{{ storage_location.temperature_range or '-' }}</td>
                                </tr>
                                <tr>
                                    <th>状态</th>
                                    <td>
                                        {% if storage_location.status == '正常' %}
                                        <span class="badge badge-success">正常</span>
                                        {% elif storage_location.status == '维护中' %}
                                        <span class="badge badge-warning">维护中</span>
                                        {% elif storage_location.status == '已关闭' %}
                                        <span class="badge badge-danger">已关闭</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>备注</th>
                                    <td>{{ storage_location.notes or '-' }}</td>
                                </tr>
                                <tr>
                                    <th>操作</th>
                                    <td>
                                        <button type="button" class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#deleteModal">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <!-- 库存列表 -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h4 class="card-title">库存列表</h4>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>食材名称</th>
                                            <th>批次号</th>
                                            <th>数量</th>
                                            <th>单位</th>
                                            <th>生产日期</th>
                                            <th>过期日期</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for inventory in inventories %}
                                        <tr>
                                            <td>{{ inventory.ingredient.name }}</td>
                                            <td>{{ inventory.batch_number }}</td>
                                            <td>{{ inventory.quantity }}</td>
                                            <td>{{ inventory.unit }}</td>
                                            <td>{{  inventory.production_date|format_datetime('%Y-%m-%d')  }}</td>
                                            <td>{{  inventory.expiry_date|format_datetime('%Y-%m-%d')  }}</td>
                                            <td>
                                                {% if inventory.status == '正常' %}
                                                <span class="badge badge-success">正常</span>
                                                {% elif inventory.status == '待检' %}
                                                <span class="badge badge-warning">待检</span>
                                                {% elif inventory.status == '冻结' %}
                                                <span class="badge badge-info">冻结</span>
                                                {% elif inventory.status == '已过期' %}
                                                <span class="badge badge-danger">已过期</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% else %}
                                        <tr>
                                            <td colspan="7" class="text-center">暂无库存数据</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                确定要删除存储位置 "{{ storage_location.name }}" 吗？此操作不可撤销。
                {% if inventories %}
                <div class="alert alert-warning mt-3">
                    <i class="fas fa-exclamation-triangle"></i> 警告：该存储位置有关联的库存记录，无法删除。请先移动或清空库存。
                </div>
                {% endif %}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form action="{{ url_for('storage_location.delete', id=storage_location.id) }}" method="post"><button type="submit" class="btn btn-danger" {% if inventories %}disabled{% endif %}>确认删除</button>
                
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
