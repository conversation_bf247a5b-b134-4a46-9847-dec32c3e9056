{% extends 'base.html' %}

{% block title %}登录 - {{ super() }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-6 col-md-8 col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0 text-center">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    用户登录
                </h4>
            </div>
            <div class="card-body p-4">
                <form method="post" novalidate>
        {{ csrf_token() }}
                    {{ form.hidden_tag() }}
                    <div class="mb-3">
                        {{ form.username.label(class="form-label fw-bold") }}
                        <div class="input-group">
                            <div class="">
                                <span class="input-group-text">
                                    <i class="fas fa-user"></i>
                                </span>
                            </div>
                            {{ form.username(class="form-control form-control-lg", placeholder="请输入用户名") }}
                        </div>
                        {% for error in form.username.errors %}
                        <small class="text-danger d-block mt-1">
                            <i class="fas fa-exclamation-circle"></i> {{ error }}
                        </small>
                        {% endfor %}
                    </div>
                    <div class="mb-3">
                        {{ form.password.label(class="form-label fw-bold") }}
                        <div class="input-group">
                            <div class="">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                            </div>
                            {{ form.password(class="form-control form-control-lg", placeholder="请输入密码") }}
                        </div>
                        {% for error in form.password.errors %}
                        <small class="text-danger d-block mt-1">
                            <i class="fas fa-exclamation-circle"></i> {{ error }}
                        </small>
                        {% endfor %}
                    </div>
                    <div class="mb-3">
                        <div class="custom-control custom-checkbox">
                            {{ form.remember_me(class="custom-control-input") }}
                            {{ form.remember_me.label(class="custom-control-label") }}
                        </div>
                    </div>
                    <div class="mb-3 mb-0">
                        {{ form.submit(class="btn btn-primary btn-lg btn-block") }}
                    </div>
                </form>
            </div>
            <div class="card-footer text-center bg-light">
                <p class="mb-0">
                    <small class="text-muted">还没有账号？</small>
                    <a href="{{ url_for('auth.register') }}" class="fw-bold">立即注册</a>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
