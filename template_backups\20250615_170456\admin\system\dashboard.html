{% extends 'base.html' %}

{% block title %}系统管理仪表盘 - {{ super() }}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .dashboard-card {
        transition: transform 0.3s;
        margin-bottom: 20px;
        height: 100%;
    }
    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .dashboard-icon {
        font-size: 2.5rem;
        margin-bottom: 15px;
        color: #fff;
        background-color: var(--primary);
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .stat-card {
        border-radius: 10px;
        border: none;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }
    .stat-card .card-body {
        padding: 1.5rem;
    }
    .stat-value {
        font-size: 2rem;
        font-weight: bold;
    }
    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
    }
    .progress {
        height: 10px;
        border-radius: 5px;
    }
    .section-title {
        border-start: 4px solid var(--primary);
        padding-left: 10px;
        margin-bottom: 20px;
    }
    .quick-actions .btn {
        margin-bottom: 10px;
    }
</style>

{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2>系统管理仪表盘</h2>
        <p class="text-muted">欢迎使用系统管理仪表盘，您可以在这里管理系统的各项功能</p>
    </div>
    <div class="col-md-4 text-end">
        <button id="refreshDashboard" class="btn btn-info">
            <i class="fas fa-sync-alt"></i> 刷新数据
        </button>
        <a href="{{ url_for('main.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回首页
        </a>
    </div>
</div>

<!-- 系统概览 -->
<div class="row mb-4">
    <div class="col-12">
        <h4 class="section-title">系统概览</h4>
    </div>
    <div class="col-md-4">
        <div class="card stat-card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="stat-label">用户数量</h5>
                        <h2 class="stat-value">{{ system_info.get('database', {}).get('users', 0) }}</h2>
                    </div>
                    <div>
                        <i class="fas fa-users fa-3x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card stat-card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="stat-label">供应商数量</h5>
                        <h2 class="stat-value">{{ system_info.get('database', {}).get('suppliers', 0) }}</h2>
                    </div>
                    <div>
                        <i class="fas fa-truck fa-3x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card stat-card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="stat-label">食谱数量</h5>
                        <h2 class="stat-value">{{ system_info.get('database', {}).get('recipes', 0) }}</h2>
                    </div>
                    <div>
                        <i class="fas fa-utensils fa-3x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系统管理功能模块 -->
<div class="row mb-4">
    <div class="col-12">
        <h4 class="section-title">系统管理功能</h4>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card">
            <div class="card-body text-center">
                <div class="dashboard-icon mx-auto" style="background-color: #4e73df;">
                    <i class="fas fa-cogs"></i>
                </div>
                <h5 class="card-title">系统设置</h5>
                <p class="card-text">配置系统基本参数，包括项目名称、显示设置等</p>
                <a href="{{ url_for('system.settings') }}" class="btn btn-primary">
                    <i class="fas fa-wrench"></i> 进入设置
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card">
            <div class="card-body text-center">
                <div class="dashboard-icon mx-auto" style="background-color: #6f42c1;">
                    <i class="fas fa-palette"></i>
                </div>
                <h5 class="card-title">主题管理</h5>
                <p class="card-text">切换系统主题颜色，个性化界面外观</p>
                <div class="mb-3">
                    <select class="form-control theme-selector" id="admin-theme-selector" data-onchange="switchAdminTheme(this.value)">
                        <optgroup label="🎨 现代专业系列">
                            <option value="primary">🌊 海洋蓝主题</option>
                            <option value="secondary">🔘 现代灰主题</option>
                            <option value="success">🌿 自然绿主题</option>
                            <option value="warning">🔥 活力橙主题</option>
                            <option value="info">💜 优雅紫主题</option>
                            <option value="danger">❤️ 深邃红主题</option>
                            <option value="dark">🌙 深色主题</option>
                        </optgroup>
                        <optgroup label="✨ 经典优雅系列">
                            <option value="classic-neutral">🏛️ 经典中性风</option>
                            <option value="modern-neutral">🏢 现代中性风</option>
                            <option value="noble-elegant">👑 贵族典雅风</option>
                            <option value="royal-solemn">🎭 皇室庄重风</option>
                        </optgroup>
                    </select>
                </div>
                <a href="{{ url_for('system.settings') }}#显示设置" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-cog"></i> 高级设置
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card">
            <div class="card-body text-center">
                <div class="dashboard-icon mx-auto" style="background-color: #e74a3b;">
                    <i class="fas fa-database"></i>
                </div>
                <h5 class="card-title">数据管理</h5>
                <p class="card-text">管理系统数据，包括超级删除功能</p>
                <a href="{{ url_for('admin_data.super_delete_index') }}" class="btn btn-danger">
                    <i class="fas fa-trash"></i> 超级删除
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card">
            <div class="card-body text-center">
                <div class="dashboard-icon mx-auto" style="background-color: #1cc88a;">
                    <i class="fas fa-user-shield"></i>
                </div>
                <h5 class="card-title">用户权限</h5>
                <p class="card-text">管理系统用户、角色和权限设置</p>
                <div class="d-flex flex-wrap justify-content-center">
                    <a href="{{ url_for('system.users') }}" class="btn btn-success btn-sm m-1">
                        <i class="fas fa-users-cog"></i> 用户
                    </a>
                    <a href="{{ url_for('system.roles') }}" class="btn btn-success btn-sm m-1">
                        <i class="fas fa-user-tag"></i> 角色
                    </a>
                    <a href="{{ url_for('system.module_visibility') }}" class="btn btn-info btn-sm m-1">
                        <i class="fas fa-eye"></i> 模块
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card dashboard-card">
            <div class="card-body text-center">
                <div class="dashboard-icon mx-auto" style="background-color: #f6c23e;">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <h5 class="card-title">新用户引导</h5>
                <p class="card-text">管理新用户引导系统、视频资源和演示数据</p>
                <a href="{{ url_for('guide_management.dashboard') }}" class="btn btn-warning">
                    <i class="fas fa-graduation-cap"></i> 引导管理
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card dashboard-card">
            <div class="card-body text-center">
                <div class="dashboard-icon mx-auto" style="background-color: #17a2b8;">
                    <i class="fas fa-images"></i>
                </div>
                <h5 class="card-title">首页轮播图</h5>
                <p class="card-text">管理首页轮播图片，支持多图上传和排序</p>
                <a href="/admin/carousel" class="btn btn-info">
                    <i class="fas fa-images"></i> 轮播图管理
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card dashboard-card">
            <div class="card-body text-center">
                <div class="dashboard-icon mx-auto" style="background-color: #28a745;">
                    <i class="fas fa-comments"></i>
                </div>
                <h5 class="card-title">在线咨询管理</h5>
                <p class="card-text">查看和回复用户咨询，管理留言记录</p>
                <a href="/consultation/list" class="btn btn-success">
                    <i class="fas fa-comments"></i> 咨询管理
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 业务管理功能模块 -->
<div class="row mb-4">
    <div class="col-12">
        <h4 class="section-title">业务管理功能</h4>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card">
            <div class="card-body text-center">
                <div class="dashboard-icon mx-auto" style="background-color: #4285f4;">
                    <i class="fas fa-truck"></i>
                </div>
                <h5 class="card-title">供应商管理</h5>
                <p class="card-text">管理食材供应商信息、资质和供货记录</p>
                <a href="{{ url_for('supplier.index') }}" class="btn btn-primary">
                    <i class="fas fa-truck"></i> 供应商管理
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card">
            <div class="card-body text-center">
                <div class="dashboard-icon mx-auto" style="background-color: #0F9D58;">
                    <i class="fas fa-carrot"></i>
                </div>
                <h5 class="card-title">食材管理</h5>
                <p class="card-text">管理食材信息、分类、库存和采购记录</p>
                <a href="{{ url_for('ingredient.index') }}" class="btn btn-success">
                    <i class="fas fa-carrot"></i> 食材管理
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card">
            <div class="card-body text-center">
                <div class="dashboard-icon mx-auto" style="background-color: #DB4437;">
                    <i class="fas fa-utensils"></i>
                </div>
                <h5 class="card-title">食谱管理</h5>
                <p class="card-text">管理菜谱、食谱、营养分析和膳食计划</p>
                <a href="{{ url_for('recipe.index') }}" class="btn btn-danger">
                    <i class="fas fa-utensils"></i> 食谱管理
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card">
            <div class="card-body text-center">
                <div class="dashboard-icon mx-auto" style="background-color: #F4B400;">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <h5 class="card-title">周菜单管理</h5>
                <p class="card-text">管理周菜单计划、发布和打印</p>
                <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-warning">
                    <i class="fas fa-calendar-alt"></i> 周菜单管理
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 系统工具和快速操作 -->
<div class="row mb-4">
    <div class="col-12">
        <h4 class="section-title">系统工具和快速操作</h4>
    </div>
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-tools"></i> 系统维护工具
            </div>
            <div class="card-body quick-actions">
                <div class="row">
                    <div class="col-md-6 mb-2">
                        <a href="{{ url_for('system_fix.index') }}" class="btn btn-outline-warning btn-block">
                            <i class="fas fa-tools"></i> 系统修复工具
                        </a>
                    </div>
                    <div class="col-md-6 mb-2">
                        <a href="{{ url_for('role_permissions_fix.fix_all_role_permissions') }}" class="btn btn-outline-danger btn-block">
                            <i class="fas fa-key"></i> 修复角色权限
                        </a>
                    </div>
                    <div class="col-md-6 mb-2">
                        <a href="{{ url_for('fix_purchase.index') }}" class="btn btn-outline-primary btn-block">
                            <i class="fas fa-shopping-cart"></i> 修复采购员权限
                        </a>
                    </div>
                    <div class="col-md-6 mb-2">
                        <a href="{{ url_for('permission_migration.index') }}" class="btn btn-outline-info btn-block">
                            <i class="fas fa-sync-alt"></i> 权限迁移工具
                        </a>
                    </div>
                    <div class="col-md-6 mb-2">
                        <a href="{{ url_for('security_admin.dashboard') }}" class="btn btn-outline-warning btn-block">
                            <i class="fas fa-shield-alt"></i> 安全仪表盘
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <i class="fas fa-bolt"></i> 快速访问
            </div>
            <div class="card-body quick-actions">
                <div class="row">
                    <div class="col-md-6 mb-2">
                        <a href="{{ url_for('food_sample.index') }}" class="btn btn-outline-success btn-block">
                            <i class="fas fa-vial"></i> 留样管理
                        </a>
                    </div>
                    <div class="col-md-6 mb-2">
                        <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-outline-warning btn-block">
                            <i class="fas fa-calendar-alt"></i> 周菜单列表
                        </a>
                    </div>
                    <div class="col-md-6 mb-2">
                        <a href="{{ url_for('purchase_order.index') }}" class="btn btn-outline-primary btn-block">
                            <i class="fas fa-shopping-cart"></i> 采购订单
                        </a>
                    </div>
                    <div class="col-md-6 mb-2">
                        <a href="{{ url_for('inventory.index') }}" class="btn btn-outline-secondary btn-block">
                            <i class="fas fa-warehouse"></i> 库存管理
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系统状态 -->
<div class="row">
    <div class="col-12">
        <h4 class="section-title">系统状态</h4>
    </div>
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <i class="fas fa-info-circle"></i> 系统信息
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="card border-start-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row g-0 align-items-center">
                                    <div class="col me-2">
                                        <div class="text-xs fw-bold text-primary text-uppercase mb-1">当前时间</div>
                                        <div class="h5 mb-0 fw-bold text-gray-800" id="current-time">{{ now.strftime('%Y-%m-%d %H:%M:%S') }}</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card border-start-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row g-0 align-items-center">
                                    <div class="col me-2">
                                        <div class="text-xs fw-bold text-success text-uppercase mb-1">食材数量</div>
                                        <div class="h5 mb-0 fw-bold text-gray-800">{{ system_info.get('database', {}).get('ingredients', 0) }}</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-carrot fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card border-start-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row g-0 align-items-center">
                                    <div class="col me-2">
                                        <div class="text-xs fw-bold text-info text-uppercase mb-1">系统状态</div>
                                        <div class="h5 mb-0 fw-bold text-gray-800">正常运行</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 刷新仪表盘数据
        $('#refreshDashboard').click(function() {
            location.reload();
        });

        // 实时更新时间
        function updateCurrentTime() {
            var now = new Date();
            var year = now.getFullYear();
            var month = (now.getMonth() + 1).toString().padStart(2, '0');
            var day = now.getDate().toString().padStart(2, '0');
            var hours = now.getHours().toString().padStart(2, '0');
            var minutes = now.getMinutes().toString().padStart(2, '0');
            var seconds = now.getSeconds().toString().padStart(2, '0');

            var formattedTime = year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
            $('#current-time').text(formattedTime);
        }

        // 初始更新时间
        updateCurrentTime();

        // 每秒更新一次时间
        setInterval(updateCurrentTime, 1000);

        // 初始化主题选择器
        initAdminThemeSelector();
    });

    // 管理员主题切换函数
    function switchAdminTheme(themeName) {
        if (window.themeSwitcher) {
            window.themeSwitcher.applyTheme(themeName);

            // 显示切换成功提示
            if (typeof toastr !== 'undefined') {
                const themeNames = {
                    // 现代专业系列
                    'primary': '海洋蓝',
                    'secondary': '现代灰',
                    'success': '自然绿',
                    'warning': '活力橙',
                    'info': '优雅紫',
                    'danger': '深邃红',
                    'dark': '深色',
                    // 经典优雅系列
                    'classic-neutral': '经典中性风',
                    'modern-neutral': '现代中性风',
                    'noble-elegant': '贵族典雅风',
                    'royal-solemn': '皇室庄重风'
                };
                toastr.success(`已切换到 ${themeNames[themeName] || themeName} 主题`, '主题切换');
            }
        } else {
            console.warn('主题切换器未加载');
        }
    }

    // 初始化管理员主题选择器
    function initAdminThemeSelector() {
        const selector = document.getElementById('admin-theme-selector');
        if (!selector) return;

        // 设置当前主题
        setTimeout(function() {
            if (window.themeSwitcher) {
                const currentTheme = window.themeSwitcher.getCurrentTheme();
                selector.value = currentTheme;
            }
        }, 500);
    }
</script>

{% endblock %}
