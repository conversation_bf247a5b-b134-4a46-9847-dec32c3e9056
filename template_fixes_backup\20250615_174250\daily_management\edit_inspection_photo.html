{% extends 'base.html' %}

{% block title %}编辑检查照片{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .photo-preview {
        max-width: 100%;
        max-height: 200px;
        margin-top: 10px;
    }
    .rating-container {
        display: flex;
        flex-direction: row;
    }
    .rating-star {
        font-size: 24px;
        color: #ccc;
        cursor: pointer;
        margin-right: 5px;
    }
    .rating-star.active {
        color: #f8ce0b;
    }
    .current-photo {
        max-width: 100%;
        max-height: 300px;
        margin-bottom: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">编辑检查照片</h1>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 fw-bold text-primary">检查信息</h6>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data" novalidate>
                {{ form.csrf_token }}
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="inspection_type">检查类型 <span class="text-danger">*</span></label>
                            <select class="form-control" id="inspection_type" name="inspection_type" required>
                                <option value="">请选择检查类型</option>
                                <option value="morning" {% if photo.reference_type == 'morning' %}selected{% endif %}>早晨检查</option>
                                <option value="noon" {% if photo.reference_type == 'noon' %}selected{% endif %}>中午检查</option>
                                <option value="evening" {% if photo.reference_type == 'evening' %}selected{% endif %}>晚上检查</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="upload_time">上传时间</label>
                            <input type="text" class="form-control" id="upload_time" value="{{ photo.upload_time|format_datetime }}" readonly>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label>评分</label>
                            <div class="rating-container" id="rating-container">
                                <span class="rating-star {% if photo.rating >= 1 %}active{% endif %}" data-value="1">★</span>
                                <span class="rating-star {% if photo.rating >= 2 %}active{% endif %}" data-value="2">★</span>
                                <span class="rating-star {% if photo.rating >= 3 %}active{% endif %}" data-value="3">★</span>
                                <span class="rating-star {% if photo.rating >= 4 %}active{% endif %}" data-value="4">★</span>
                                <span class="rating-star {% if photo.rating >= 5 %}active{% endif %}" data-value="5">★</span>
                            </div>
                            <input type="hidden" name="rating" id="rating_input" value="{{ photo.rating or 0 }}">
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="description">备注说明</label>
                    <textarea class="form-control" id="description" name="description" rows="3">{{ photo.description or '' }}</textarea>
                </div>

                <div class="mb-3">
                    <label>当前照片</label>
                    <div class="text-center">
                        <img src="{{ photo.file_path }}" class="current-photo" alt="当前检查照片">
                    </div>
                </div>

                <div class="mb-3">
                    <label for="new_photo">更新照片（可选）</label>
                    <input type="file" class="form-control-file" id="new_photo" name="new_photo" accept="image/*">
                    <small class="form-text text-muted">如需更换照片，请选择新照片上传，支持jpg、jpeg、png格式</small>
                    <div id="photo-preview" class="mt-2"></div>
                </div>

                <div class="mb-3 mt-4">
                    <button type="submit" class="btn btn-primary">保存</button>
                    <a href="{{ url_for('daily_management.view_inspection_photo', photo_id=photo.id) }}" class="btn btn-secondary">取消</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    // 星级评分
    function setupRating() {
        const container = document.getElementById('rating-container');
        const input = document.getElementById('rating_input');
        const stars = container.querySelectorAll('.rating-star');

        stars.forEach(star => {
            star.addEventListener('click', function() {
                const value = parseInt(this.getAttribute('data-value'));
                input.value = value;

                // 更新星星显示
                stars.forEach(s => {
                    if (parseInt(s.getAttribute('data-value')) <= value) {
                        s.classList.add('active');
                    } else {
                        s.classList.remove('active');
                    }
                });
            });

            star.addEventListener('mouseover', function() {
                const value = parseInt(this.getAttribute('data-value'));

                // 临时更新星星显示
                stars.forEach(s => {
                    if (parseInt(s.getAttribute('data-value')) <= value) {
                        s.classList.add('active');
                    } else {
                        s.classList.remove('active');
                    }
                });
            });

            container.addEventListener('mouseout', function() {
                const value = parseInt(input.value);

                // 恢复星星显示
                stars.forEach(s => {
                    if (parseInt(s.getAttribute('data-value')) <= value) {
                        s.classList.add('active');
                    } else {
                        s.classList.remove('active');
                    }
                });
            });
        });
    }

    // 照片预览
    document.getElementById('new_photo').addEventListener('change', function(e) {
        const previewDiv = document.getElementById('photo-preview');
        previewDiv.innerHTML = '';

        if (this.files && this.files[0]) {
            const reader = new FileReader();
            reader.onload = function(event) {
                const img = document.createElement('img');
                img.src = event.target.result;
                img.className = 'photo-preview';
                previewDiv.appendChild(img);
            }
            reader.readAsDataURL(this.files[0]);
        }
    });

    // 初始化星级评分
    setupRating();
</script>
{% endblock %}
