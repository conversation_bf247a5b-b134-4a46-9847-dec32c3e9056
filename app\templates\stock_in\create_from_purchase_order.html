{% extends 'base.html' %}

{% block title %}从采购订单创建入库单 - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
    /* 流程指引样式 */
    .process-flow {
        display: flex;
        justify-content: space-between;
        margin-bottom: 30px;
        position: relative;
    }

    .process-flow::before {
        content: '';
        position: absolute;
        top: 30px;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #e9ecef;
        z-index: 1;
    }

    .process-step {
        position: relative;
        z-index: 2;
        text-align: center;
        width: 20%;
    }

    .step-number {
        width: 60px;
        height: 60px;
        line-height: 60px;
        border-radius: 50%;
        background-color: #e9ecef;
        color: #6c757d;
        font-size: 24px;
        font-weight: bold;
        margin: 0 auto 15px;
        position: relative;
    }

    .step-active .step-number {
        background-color: #007bff;
        color: white;
    }

    .step-completed .step-number {
        background-color: #28a745;
        color: white;
    }

    .step-completed .step-number::after {
        content: '✓';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        font-size: 30px;
        line-height: 60px;
    }

    .step-title {
        font-weight: bold;
        margin-bottom: 5px;
    }

    .step-description {
        color: #6c757d;
        font-size: 0.85rem;
    }

    /* 表单样式 */
    .form-section {
        background-color: #f8f9fa;
        border-radius: 5px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .form-section-title {
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 10px;
        margin-bottom: 20px;
        font-weight: bold;
        color: #495057;
    }

    .form-control:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    /* 表格样式 */
    .table-items th {
        background-color: #f8f9fa;
        font-weight: 600;
    }

    .table-items td {
        vertical-align: middle;
    }

    .ingredient-name {
        font-weight: 500;
        color: #212529;
    }

    .ingredient-category {
        font-size: 0.85rem;
        color: #6c757d;
    }

    .quantity-input {
        font-weight: bold;
        color: #dc3545;
        text-align: center;
    }

    /* 去掉数量输入框的上下滚动键 */
    .quantity-input::-webkit-outer-spin-button,
    .quantity-input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    .quantity-input[type=number] {
        -moz-appearance: textfield;
    }

    /* 价格输入框样式 */
    .price-input {
        font-weight: bold;
        color: #dc3545;
        text-align: center;
        font-size: 1.2rem;
    }

    /* 去掉价格输入框的上下滚动键 */
    .price-input::-webkit-outer-spin-button,
    .price-input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    .price-input[type=number] {
        -moz-appearance: textfield;
    }

    /* 提示框样式 */
    .alert-guidance {
        border-start: 4px solid #17a2b8;
        background-color: #f8f9fa;
    }

    .alert-icon {
        font-size: 1.5rem;
        margin-right: 15px;
    }

    /* 操作按钮样式 */
    .btn-action {
        min-width: 120px;
    }


</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">从采购订单创建入库单</h1>
        <div>
            <a href="{{ url_for('purchase_order.view', id=order.id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> 返回采购订单
            </a>
        </div>
    </div>

    <!-- 流程指引 -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-info-circle"></i> 入库流程指引</h5>
        </div>
        <div class="card-body">
            <div class="process-flow">
                <div class="process-step step-completed">
                    <div class="step-number">1</div>
                    <div class="step-title">创建采购订单</div>
                    <div class="step-description">已完成</div>
                </div>
                <div class="process-step step-completed">
                    <div class="step-number">2</div>
                    <div class="step-title">确认采购订单</div>
                    <div class="step-description">已完成</div>
                </div>
                <div class="process-step step-completed">
                    <div class="step-number">3</div>
                    <div class="step-title">供应商送货</div>
                    <div class="step-description">已完成</div>
                </div>
                <div class="process-step step-active">
                    <div class="step-number">4</div>
                    <div class="step-title">创建入库单</div>
                    <div class="step-description">当前步骤</div>
                </div>
                <div class="process-step">
                    <div class="step-number">5</div>
                    <div class="step-title">确认入库</div>
                    <div class="step-description">更新库存</div>
                </div>
            </div>

            <div class="alert alert-guidance">
                <div class="d-flex">
                    <div class="me-3">
                        <i class="fas fa-lightbulb alert-icon text-warning"></i>
                    </div>
                    <div>
                        <h5 class="alert-heading">入库操作指南</h5>
                        <p>在此页面，您需要：</p>
                        <ol>
                            <li>检查采购订单中的食材信息是否正确</li>
                            <li>调整实际入库数量（如有差异）</li>
                            <li>上传相关检验检疫证明文件（如需要）</li>
                            <li>确认入库信息并提交</li>
                        </ol>
                        <p class="mb-0">提交后，系统将创建入库单，等待仓库管理员确认后更新库存。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 采购订单信息 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-file-invoice"></i> 采购订单信息</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <p class="mb-1"><strong>订单编号：</strong></p>
                    <p class="mb-3">{{ order.order_number }}</p>
                </div>
                <div class="col-md-3">
                    <p class="mb-1"><strong>供应商：</strong></p>
                    <p class="mb-3">{{ order.supplier.name if order.supplier else '自购' }}</p>
                </div>
                <div class="col-md-3">
                    <p class="mb-1"><strong>订单日期：</strong></p>
                    <p class="mb-3">{{ order.order_date.strftime('%Y-%m-%d') if order.order_date else '' }}</p>
                </div>
                <div class="col-md-3">
                    <p class="mb-1"><strong>订单状态：</strong></p>
                    <p class="mb-3">
                        <span class="badge badge-info">{{ order.status }}</span>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- 入库表单 -->
    <form id="stockInForm" method="post">
        {{ csrf_token() }}
        <input type="hidden" name="order_id" value="{{ order.id }}">
        <input type="hidden" name="warehouse_id" value="{{ warehouse.id }}">

        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-clipboard-list"></i> 入库信息</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="warehouseName"><strong>入库仓库</strong></label>
                            <input type="text" class="form-control" id="warehouseName" value="{{ warehouse.name }}" readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="stockInDate"><strong>入库日期</strong> <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="stockInDate" name="stock_in_date" value="{{ now.strftime('%Y-%m-%d') }}" required>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="notes"><strong>备注</strong></label>
                    <textarea class="form-control" id="notes" name="notes" rows="2" placeholder="请输入入库备注信息"></textarea>
                </div>
            </div>
        </div>

        <!-- 入库明细 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list"></i> 入库明细</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-items" id="itemsTable">
                        <thead>
                            <tr>
                                <th style="width: 5%">#</th>
                                <th class="w-20">食材名称</th>
                                <th class="w-15">订单数量</th>
                                <th class="w-15">实际入库数量</th>
                                <th style="width: 10%">单位</th>
                                <th class="w-15">单价</th>
                                <th class="w-20">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in order_items %}
                            <tr data-item-id="{{ item.id }}">
                                <td>{{ loop.index }}</td>
                                <td>
                                    <div class="ingredient-name">{{ item.ingredient.name }}</div>
                                    <div class="ingredient-category">{{ item.ingredient.category }}</div>
                                    <input type="hidden" name="ingredient_id[]" value="{{ item.ingredient_id }}">
                                </td>
                                <td>
                                    <div class="text-center">{{ item.quantity }} {{ item.unit }}</div>
                                    <input type="hidden" name="order_quantity[]" value="{{ item.quantity }}">
                                </td>
                                <td>
                                    <input type="number" class="form-control quantity-input" name="actual_quantity[]" value="{{ item.quantity }}" min="0" step="0.01" required>
                                </td>
                                <td>
                                    <input type="text" class="form-control" name="unit[]" value="{{ item.unit }}" readonly>
                                </td>
                                <td>
                                    <input type="number" class="form-control price-input" name="unit_price[]" value="{{ item.unit_price|float }}" min="0" step="0.01">
                                </td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-outline-danger remove-item-btn">
                                        <i class="fas fa-trash"></i> 移除
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>提示：</strong> 如果实际入库数量与订单数量不符，请调整"实际入库数量"字段。如需移除某项食材，请点击"移除"按钮。供应商信息和检验检疫证明可在后续的批次编辑器中完善。
                </div>
            </div>
        </div>

        <!-- 提交按钮 -->
        <div class="card mb-4">
            <div class="card-body text-end">
                <a href="{{ url_for('purchase_order.view', id=order.id) }}" class="btn btn-secondary btn-action me-2">
                    <i class="fas fa-times"></i> 取消
                </a>
                <button type="submit" class="btn btn-primary btn-action">
                    <i class="fas fa-save"></i> 创建入库单
                </button>
            </div>
        </div>
    </form>
</div>


{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 表单提交处理
        $('#stockInForm').on('submit', function(e) {
            e.preventDefault();

            // 收集表单数据
            const formData = new FormData(this);

            // 转换为JSON格式
            const jsonData = {
                order_id: formData.get('order_id'),
                warehouse_id: formData.get('warehouse_id'),
                stock_in_date: formData.get('stock_in_date'),
                notes: formData.get('notes'),
                items: []
            };

            // 收集明细数据
            const rows = $('#itemsTable tbody tr:visible');
            rows.each(function() {
                const $row = $(this);
                const itemId = $row.data('item-id');
                const ingredientId = $row.find('input[name="ingredient_id[]"]').val();
                const orderQuantity = parseFloat($row.find('input[name="order_quantity[]"]').val());
                const actualQuantity = parseFloat($row.find('input[name="actual_quantity[]"]').val());
                const unit = $row.find('input[name="unit[]"]').val();
                const unitPrice = parseFloat($row.find('input[name="unit_price[]"]').val());

                jsonData.items.push({
                    id: itemId,
                    ingredient_id: ingredientId,
                    order_quantity: orderQuantity,
                    actual_quantity: actualQuantity,
                    unit: unit,
                    unit_price: unitPrice
                });
            });

            // 发送AJAX请求
            $.ajax({
                url: "{{ url_for('stock_in.save_from_purchase_order') }}",
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(jsonData),
                beforeSend: function() {
                    // 显示加载状态
                    $('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 处理中...');
                },
                success: function(response) {
                    if (response.success) {
                        // 显示成功消息
                        alert(response.message);
                        // 跳转到入库单详情页
                        window.location.href = response.redirect_url;
                    } else {
                        // 显示错误消息
                        alert('创建入库单失败: ' + response.message);
                        $('button[type="submit"]').prop('disabled', false).html('<i class="fas fa-save"></i> 创建入库单');
                    }
                },
                error: function(xhr) {
                    // 显示错误消息
                    alert('创建入库单请求失败，请稍后重试');
                    $('button[type="submit"]').prop('disabled', false).html('<i class="fas fa-save"></i> 创建入库单');
                }
            });
        });

        // 移除食材按钮点击事件
        $(document).on('click', '.remove-item-btn', function() {
            if (confirm('确定要从入库单中移除此食材吗？')) {
                $(this).closest('tr').fadeOut(300, function() {
                    $(this).hide();
                });
            }
        });


    });
</script>
{% endblock %}
