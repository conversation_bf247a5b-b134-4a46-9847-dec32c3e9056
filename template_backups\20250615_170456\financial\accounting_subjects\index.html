{% extends "financial/base.html" %}

{% block page_title %}会计科目管理{% endblock %}

{% block breadcrumb %}
<span class="uf-breadcrumb-item active">会计科目管理</span>
{% endblock %}

{% block page_actions %}
<a href="{{ url_for('financial.create_accounting_subject') }}" class="uf-btn uf-btn-primary">
    <i class="fas fa-plus uf-icon"></i> 新增科目
</a>
<a href="{{ url_for('financial.accounting_subjects_text_tree') }}" class="uf-btn">
    <i class="fas fa-sitemap uf-icon"></i> 树形视图
</a>
<a href="{{ url_for('help.accounting_subjects_help') }}" class="uf-btn uf-btn-info">
    <i class="fas fa-question-circle uf-icon"></i> 帮助
</a>
{% endblock %}

{% block financial_content %}
<!-- 用友财务软件风格搜索表单 -->
<div style="background: #f8f9fa; border: 1px solid #c0c0c0; padding: 8px; margin-bottom: 8px; border-radius: 1px;">
    <form method="GET" action="{{ url_for('financial.accounting_subjects_index') }}" style="margin: 0;">
        <div style="display: flex; align-items: center; gap: 12px; flex-wrap: wrap;">
            <div style="display: flex; align-items: center; gap: 4px;">
                <label style="font-size: 11px; color: #333; white-space: nowrap;">关键词：</label>
                <input type="text" id="keyword" name="keyword" value="{{ keyword }}"
                       placeholder="科目编码或名称"
                       style="width: 120px; font-size: 11px; padding: 2px 4px; border: 1px solid #c0c0c0; border-radius: 1px;">
            </div>
            <div style="display: flex; align-items: center; gap: 4px;">
                <label style="font-size: 11px; color: #333; white-space: nowrap;">科目类型：</label>
                <select id="subject_type" name="subject_type" style="width: 100px; font-size: 11px; padding: 2px 4px; border: 1px solid #c0c0c0; border-radius: 1px;">
                    <option value="">全部类型</option>
                    <option value="资产" {% if subject_type == '资产' %}selected{% endif %}>资产</option>
                    <option value="负债" {% if subject_type == '负债' %}selected{% endif %}>负债</option>
                    <option value="所有者权益" {% if subject_type == '所有者权益' %}selected{% endif %}>所有者权益</option>
                    <option value="收入" {% if subject_type == '收入' %}selected{% endif %}>收入</option>
                    <option value="费用" {% if subject_type == '费用' %}selected{% endif %}>费用</option>
                </select>
            </div>
            <div style="display: flex; gap: 4px;">
                <button type="submit" class="uf-btn uf-btn-primary uf-btn-sm">
                    <i class="fas fa-search" style="font-size: 10px;"></i> 查询
                </button>
                <a href="{{ url_for('financial.accounting_subjects_index') }}" class="uf-btn uf-btn-sm">
                    <i class="fas fa-undo" style="font-size: 10px;"></i> 重置
                </a>
            </div>
        </div>
    </form>
</div>

<!-- 用友财务软件专业科目列表 -->
<div class="uf-card">
    <div class="uf-card-header">
        <span>
            <i class="fas fa-list uf-icon"></i> 会计科目列表
            <span style="margin-left: 6px; padding: 1px 4px; background: var(--uf-info); color: white; border-radius: 1px; font-size: 10px;">
                共 {{ subjects.total }} 条记录
            </span>
        </span>
    </div>
    <div class="uf-card-body" style="padding: 0;">
        {% if subjects.items %}
        <table class="uf-table" style="margin: 0;">
            <thead>
                <tr>
                    <th style="width: 90px;">科目编码</th>
                    <th style="width: 160px;">科目名称</th>
                    <th style="width: 70px;">科目类型</th>
                    <th style="width: 60px;">余额方向</th>
                    <th style="width: 40px;">级别</th>
                    <th style="width: 130px;">上级科目</th>
                    <th style="width: 50px;">状态</th>
                    <th style="width: 60px;">系统科目</th>
                    <th style="width: 80px;">操作</th>
                </tr>
            </thead>
            <tbody>
                {% for subject in subjects.items %}
                <tr>
                    <td class="text-center">
                        <code class="uf-code">{{ subject.code }}</code>
                    </td>
                    <td class="text-start">
                        <div style="font-weight: 500; color: #333; font-size: 11px;">{{ subject.name }}</div>
                        {% if subject.description %}
                        <div style="font-size: 10px; color: #666; margin-top: 1px; line-height: 1.2;">{{ subject.description }}</div>
                        {% endif %}
                    </td>
                    <td class="text-center">
                        <span style="background: #e6f2ff; color: var(--uf-primary); border: 1px solid #b3d9ff; padding: 1px 3px; border-radius: 1px; font-size: 10px; white-space: nowrap;">{{ subject.subject_type }}</span>
                    </td>
                    <td class="text-center">
                        {% if subject.balance_direction == '借方' %}
                        <span style="background: #d4edda; color: #155724; border: 1px solid #c3e6cb; padding: 1px 3px; border-radius: 1px; font-size: 10px;">借方</span>
                        {% else %}
                        <span style="background: #cce5ff; color: #004085; border: 1px solid #99ccff; padding: 1px 3px; border-radius: 1px; font-size: 10px;">贷方</span>
                        {% endif %}
                    </td>
                    <td class="text-center">
                        <span style="font-weight: 500; color: var(--uf-primary); font-size: 11px;">{{ subject.level }}</span>
                    </td>
                    <td class="text-start">
                        {% if subject.parent %}
                        <div style="font-size: 10px; color: #666; line-height: 1.2;">{{ subject.parent.code }}<br>{{ subject.parent.name }}</div>
                        {% else %}
                        <span style="color: #999; font-size: 10px;">无</span>
                        {% endif %}
                    </td>
                    <td class="text-center">
                        {% if subject.is_active %}
                        <span style="background: #d4edda; color: #155724; border: 1px solid #c3e6cb; padding: 1px 3px; border-radius: 1px; font-size: 10px;">启用</span>
                        {% else %}
                        <span style="background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; padding: 1px 3px; border-radius: 1px; font-size: 10px;">禁用</span>
                        {% endif %}
                    </td>
                    <td class="text-center">
                        {% if subject.is_system %}
                        <span style="background: #f8f9fa; color: #495057; border: 1px solid #dee2e6; padding: 1px 3px; border-radius: 1px; font-size: 10px;">系统</span>
                        {% else %}
                        <span style="background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; padding: 1px 3px; border-radius: 1px; font-size: 10px;">自定义</span>
                        {% endif %}
                    </td>
                    <td class="text-center">
                        {% if not subject.is_system %}
                        <div class="uf-btn-group">
                            <a href="{{ url_for('financial.edit_accounting_subject', id=subject.id) }}"
                               class="uf-btn uf-btn-sm" title="编辑" style="padding: 1px 4px;">
                                <i class="fas fa-edit" style="font-size: 10px;"></i>
                            </a>
                            <form method="POST" action="{{ url_for('financial.delete_accounting_subject', id=subject.id) }}"
                                  style="display: inline;">
                                <button type="submit" class="uf-btn uf-btn-sm uf-btn-danger"
                                        data-uf-confirm-delete="确定要删除科目 {{ subject.name }} 吗？"
                                        title="删除" style="padding: 1px 4px;">
                                    <i class="fas fa-trash" style="font-size: 10px;"></i>
                                </button>
                            </form>
                        </div>
                        {% else %}
                        <span style="color: #999; font-size: 10px;">系统科目</span>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- 用友风格分页 -->
        {% if subjects.pages > 1 %}
        <div class="uf-pagination">
            {% if subjects.has_prev %}
            <span class="uf-page-item">
                <a class="uf-page-link" href="{{ url_for('financial.accounting_subjects_index',
                    page=subjects.prev_num, keyword=keyword, subject_type=subject_type) }}">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </span>
            {% endif %}

            {% for page_num in subjects.iter_pages() %}
                {% if page_num %}
                    {% if page_num != subjects.page %}
                    <span class="uf-page-item">
                        <a class="uf-page-link" href="{{ url_for('financial.accounting_subjects_index',
                            page=page_num, keyword=keyword, subject_type=subject_type) }}">
                            {{ page_num }}
                        </a>
                    </span>
                    {% else %}
                    <span class="uf-page-item active">
                        <span class="uf-page-link">{{ page_num }}</span>
                    </span>
                    {% endif %}
                {% else %}
                <span class="uf-page-item disabled">
                    <span class="uf-page-link">…</span>
                </span>
                {% endif %}
            {% endfor %}

            {% if subjects.has_next %}
            <span class="uf-page-item">
                <a class="uf-page-link" href="{{ url_for('financial.accounting_subjects_index',
                    page=subjects.next_num, keyword=keyword, subject_type=subject_type) }}">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </span>
            {% endif %}
        </div>
        {% endif %}

        {% else %}
        <div style="text-align: center; padding: 40px; color: #666;">
            <i class="fas fa-inbox" style="font-size: 48px; color: #ccc; margin-bottom: 16px;"></i>
            <p style="margin-bottom: 20px;">暂无会计科目数据</p>
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 2px; padding: 16px; max-width: 500px; margin: 0 auto; text-align: left;">
                <h6 style="color: #856404; margin-bottom: 12px;">
                    <i class="fas fa-exclamation-triangle"></i> 系统异常
                </h6>
                <p style="margin-bottom: 12px; font-size: 12px; color: #856404;">
                    会计科目应该在学校注册时自动创建，如果您看到此消息，说明系统可能存在问题。
                </p>
                <p style="margin-bottom: 16px; font-size: 12px; color: #856404;">
                    请联系系统管理员或查看帮助中心获取解决方案。
                </p>
                <a href="{{ url_for('help.accounting_subjects_help') }}" class="uf-btn uf-btn-info">
                    <i class="fas fa-question-circle uf-icon"></i> 查看帮助中心
                </a>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- 用友风格科目类型说明 -->
<div class="uf-card">
    <div class="uf-card-header">
        <i class="fas fa-info-circle uf-icon"></i> 会计科目说明
    </div>
    <div class="uf-card-body">
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
                <div style="margin-bottom: 16px;">
                    <h6 style="color: var(--uf-success); margin-bottom: 6px; font-size: 13px;">
                        <i class="fas fa-coins uf-icon"></i> 资产类科目
                    </h6>
                    <p style="font-size: 11px; color: #666; margin: 0; line-height: 1.4;">
                        记录学校食堂拥有的各种资产，如现金、银行存款、库存商品、固定资产等。余额方向为借方。
                    </p>
                </div>

                <div style="margin-bottom: 16px;">
                    <h6 style="color: var(--uf-danger); margin-bottom: 6px; font-size: 13px;">
                        <i class="fas fa-credit-card uf-icon"></i> 负债类科目
                    </h6>
                    <p style="font-size: 11px; color: #666; margin: 0; line-height: 1.4;">
                        记录学校食堂的各种债务，如应付账款、应付职工薪酬等。余额方向为贷方。
                    </p>
                </div>

                <div style="margin-bottom: 16px;">
                    <h6 style="color: var(--uf-info); margin-bottom: 6px; font-size: 13px;">
                        <i class="fas fa-balance-scale uf-icon"></i> 所有者权益类科目
                    </h6>
                    <p style="font-size: 11px; color: #666; margin: 0; line-height: 1.4;">
                        记录学校食堂的净资产，如实收资本、本年利润等。余额方向为贷方。
                    </p>
                </div>
            </div>
            <div>
                <div style="margin-bottom: 16px;">
                    <h6 style="color: var(--uf-success); margin-bottom: 6px; font-size: 13px;">
                        <i class="fas fa-arrow-up uf-icon"></i> 收入类科目
                    </h6>
                    <p style="font-size: 11px; color: #666; margin: 0; line-height: 1.4;">
                        记录学校食堂的各种收入，如学生餐费收入、教师餐费收入等。余额方向为贷方。
                    </p>
                </div>

                <div style="margin-bottom: 16px;">
                    <h6 style="color: var(--uf-warning); margin-bottom: 6px; font-size: 13px;">
                        <i class="fas fa-arrow-down uf-icon"></i> 费用类科目
                    </h6>
                    <p style="font-size: 11px; color: #666; margin: 0; line-height: 1.4;">
                        记录学校食堂的各种支出，如食材成本、人工费用、管理费用等。余额方向为借方。
                    </p>
                </div>

                <div style="background: #e6f2ff; border: 1px solid #b3d9ff; border-radius: 2px; padding: 12px;">
                    <div style="color: var(--uf-primary); font-weight: 600; margin-bottom: 8px; font-size: 12px;">
                        <i class="fas fa-lightbulb uf-icon"></i> 使用说明：
                    </div>
                    <ul style="margin: 0; padding-left: 16px; font-size: 11px; color: #333; line-height: 1.4;">
                        <li style="margin-bottom: 4px;">系统已预设了标准的会计科目体系，显示为"系统"标识</li>
                        <li style="margin-bottom: 4px;">您可以在系统科目下新建子科目，也可以新建独立的学校科目</li>
                        <li style="margin-bottom: 4px;">学校科目显示为"自定义"标识，可以编辑和删除</li>
                        <li style="margin-bottom: 0;">系统科目不能编辑或删除，确保会计标准的一致性</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block financial_js %}
<script>
// 会计科目管理页面特定JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // 科目类型筛选变化时自动提交表单
    document.getElementById('subject_type').addEventListener('change', function() {
        this.form.submit();
    });
});
</script>
{% endblock %}
