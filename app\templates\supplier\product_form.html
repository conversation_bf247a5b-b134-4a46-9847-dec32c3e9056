{% extends 'base.html' %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('static', filename='css/categorized-ingredient-select.css') }}">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title">{{ title }}</h3>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data" id="productForm">
        {{ csrf_token() }}
                        {{ form.hidden_tag() }}

                        <!-- 基本信息卡片 -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5><i class="fas fa-info-circle"></i> 基本信息</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.supplier_id.label }}
                                            <span class="text-danger">*</span>
                                            {{ form.supplier_id(class="form-control select2") }}
                                            {% for error in form.supplier_id.errors %}
                                            <small class="text-danger">{{ error }}</small>
                                            {% endfor %}
                                            <small class="form-text text-muted">选择产品的供应商</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.ingredient_id.label }}
                                            <span class="text-danger">*</span>
                                            {{ form.ingredient_id(class="form-control categorized-ingredient-select") }}
                                            {% for error in form.ingredient_id.errors %}
                                            <small class="text-danger">{{ error }}</small>
                                            {% endfor %}
                                            <small class="form-text text-muted">选择产品对应的食材</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- 食材信息预览 -->
                                <div id="ingredientPreview" class="alert alert-info d-none mb-3">
                                    <h6><i class="fas fa-utensils"></i> 食材信息预览</h6>
                                    <div id="ingredientDetails"></div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.product_code.label }}
                                            <div class="input-group">
                                                {{ form.product_code(class="form-control", placeholder="例如: SP-001") }}
                                                <div class="">
                                                    <button type="button" class="btn btn-outline-secondary" id="generateCodeBtn">
                                                        <i class="fas fa-sync-alt"></i> 自动生成
                                                    </button>
                                                </div>
                                            </div>
                                            {% for error in form.product_code.errors %}
                                            <small class="text-danger">{{ error }}</small>
                                            {% endfor %}
                                            <small class="form-text text-muted">可选，产品编码，便于管理和查询</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.product_name.label }}
                                            {{ form.product_name(class="form-control", placeholder="输入产品名称") }}
                                            {% for error in form.product_name.errors %}
                                            <small class="text-danger">{{ error }}</small>
                                            {% endfor %}
                                            <small class="form-text text-muted">可选，如不填写则使用食材名称</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 产品详情卡片 -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5><i class="fas fa-clipboard-list"></i> 产品详情</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            {{ form.model_number.label }}
                                            {{ form.model_number(class="form-control", placeholder="例如: M-2023") }}
                                            {% for error in form.model_number.errors %}
                                            <small class="text-danger">{{ error }}</small>
                                            {% endfor %}
                                            <small class="form-text text-muted">产品型号，便于区分不同规格</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            {{ form.specification.label }}
                                            {{ form.specification(class="form-control", placeholder="例如: 500g/袋") }}
                                            {% for error in form.specification.errors %}
                                            <small class="text-danger">{{ error }}</small>
                                            {% endfor %}
                                            <small class="form-text text-muted">产品规格，如重量、包装等</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            {{ form.price.label }}
                                            <span class="text-danger">*</span>
                                            <div class="input-group">
                                                {{ form.price(class="form-control", placeholder="0.00") }}
                                                <div class="">
                                                    <span class="input-group-text">元</span>
                                                </div>
                                            </div>
                                            {% for error in form.price.errors %}
                                            <small class="text-danger">{{ error }}</small>
                                            {% endfor %}
                                            <small class="form-text text-muted">产品单价（元）</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.lead_time.label }}
                                            <div class="input-group">
                                                {{ form.lead_time(class="form-control", placeholder="3") }}
                                                <div class="">
                                                    <span class="input-group-text">天</span>
                                                </div>
                                            </div>
                                            {% for error in form.lead_time.errors %}
                                            <small class="text-danger">{{ error }}</small>
                                            {% endfor %}
                                            <small class="form-text text-muted">从下单到送达的预计时间</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.min_order_quantity.label }}
                                            {{ form.min_order_quantity(class="form-control", placeholder="1.0") }}
                                            {% for error in form.min_order_quantity.errors %}
                                            <small class="text-danger">{{ error }}</small>
                                            {% endfor %}
                                            <small class="form-text text-muted">最小起订量，默认为1</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    {{ form.description.label }}
                                    {{ form.description(class="form-control", rows=3, placeholder="请输入产品的详细描述，包括特点、用途等信息") }}
                                    {% for error in form.description.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                    <small class="form-text text-muted">产品的详细描述，有助于采购人员了解产品</small>
                                </div>
                            </div>
                        </div>

                        <!-- 质量与认证卡片 -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5><i class="fas fa-certificate"></i> 质量与认证</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.quality_cert.label }}
                                            <span class="text-danger">*</span>
                                            {{ form.quality_cert(class="form-control", placeholder="例如: 国家标准") }}
                                            {% for error in form.quality_cert.errors %}
                                            <small class="text-danger">{{ error }}</small>
                                            {% endfor %}
                                            <small class="form-text text-muted">产品的质量认证信息</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.quality_standard.label }}
                                            {{ form.quality_standard(class="form-control", placeholder="例如: GB/T") }}
                                            {% for error in form.quality_standard.errors %}
                                            <small class="text-danger">{{ error }}</small>
                                            {% endfor %}
                                            <small class="form-text text-muted">产品遵循的质量标准</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    {{ form.product_image.label }}
                                    <div class="custom-file">
                                        {{ form.product_image(class="custom-file-input") }}
                                        <label class="custom-file-label" for="product_image">选择文件</label>
                                    </div>
                                    {% for error in form.product_image.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                    <small class="form-text text-muted">上传产品图片，支持jpg、png、jpeg格式</small>
                                    {% if product and product.product_image %}
                                    <div class="mt-2">
                                        <img src="{{ url_for('static', filename=product.product_image) }}" alt="产品图片" class="img-thumbnail" style="max-height: 150px;">
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- 提交按钮 -->
                        <div class="mb-3 text-center">
                            {{ form.submit(class="btn btn-primary btn-lg") }}
                            <a href="{{ url_for('supplier_product.index') }}" class="btn btn-secondary btn-lg ms-2">取消</a>
                        </div>

                        <!-- 提示信息 -->
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-info-circle"></i> 提示：产品添加后需要经过审核才能上架。带 <span class="text-danger">*</span> 的字段为必填项。
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/categorized-ingredient-select.js') }}"></script>
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 初始化Select2
        $('.select2').select2({
            theme: 'bootstrap4',
            width: '100%'
        });

        // 初始化分类食材选择组件
        $('.categorized-ingredient-select').categorizedIngredientSelect();

        // 文件上传显示文件名
        $('.custom-file-input').on('change', function() {
            var fileName = $(this).val().split('\\').pop();
            $(this).next('.custom-file-label').html(fileName);
        });

        // 如果URL中有supplier_id参数，自动选择对应的供应商
        var urlParams = new URLSearchParams(window.location.search);
        var supplierIdParam = urlParams.get('supplier_id');
        if (supplierIdParam) {
            $('#supplier_id').val(supplierIdParam).trigger('change');

            // 如果是新建产品且产品编码为空，自动生成产品编码
            if (!$('#product_code').val() && window.location.href.indexOf('/edit') === -1) {
                setTimeout(function() {
                    $('#generateCodeBtn').click();
                }, 500);
            }
        }

        // 供应商变更时，如果产品编码为空，提示可以自动生成编码
        $('#supplier_id').on('change', function() {
            if ($(this).val() != '0' && !$('#product_code').val()) {
                toastr.info('您可以点击"自动生成"按钮生成产品编码');
            }
        });

        // 食材选择后显示食材信息预览
        $('#ingredient_id').on('change', function() {
            var ingredientId = $(this).val();
            if (ingredientId && ingredientId != '0') {
                // 显示加载中状态
                $('#ingredientPreview').removeClass('d-none');
                $('#ingredientDetails').html('<p class="text-center"><i class="fas fa-spinner fa-spin"></i> 正在加载食材信息...</p>');

                // 通过AJAX获取食材详情
                $.ajax({
                    url: '/supplier-product/api/ingredient/' + ingredientId,
                    type: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        // 构建食材信息预览
                        var html = `
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>食材名称:</strong> ${data.name}</p>
                                    <p><strong>分类:</strong> ${data.category_name || data.category || '未分类'}</p>
                                    <p><strong>计量单位:</strong> ${data.unit || '未设置'}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>规格:</strong> ${data.specification || '未设置'}</p>
                                    <p><strong>保质期:</strong> ${data.shelf_life ? data.shelf_life + '天' : '未设置'}</p>
                                    <p><strong>存储条件:</strong> ${data.storage_condition || '未设置'}</p>
                                </div>
                            </div>
                            <p><small>选择食材后，系统会自动填充相关信息。如需修改，请直接编辑对应字段。</small></p>
                        `;

                        $('#ingredientDetails').html(html);

                        // 自动填充相关字段
                        if (!$('#product_name').val()) {
                            $('#product_name').val(data.name);
                        }

                        if (!$('#specification').val() && data.specification) {
                            $('#specification').val(data.specification);
                        }
                    },
                    error: function() {
                        $('#ingredientDetails').html('<p class="text-danger"><i class="fas fa-exclamation-triangle"></i> 获取食材信息失败，请重试或手动填写信息。</p>');
                    }
                });
            } else {
                $('#ingredientPreview').addClass('d-none');
            }
        });

        // 自动生成产品编码
        $('#generateCodeBtn').on('click', function() {
            var supplierId = $('#supplier_id').val();
            if (supplierId == '0') {
                toastr.warning('请先选择供应商，以便生成更精确的产品编码');
                return;
            }

            // 显示加载状态
            var $btn = $(this);
            var originalHtml = $btn.html();
            $btn.html('<i class="fas fa-spinner fa-spin"></i> 生成中...');
            $btn.prop('disabled', true);

            // 调用API获取产品编码
            $.ajax({
                url: '/supplier-product/api/generate-product-code',
                type: 'GET',
                data: {
                    supplier_id: supplierId
                },
                dataType: 'json',
                success: function(data) {
                    if (data.product_code) {
                        $('#product_code').val(data.product_code);
                        toastr.success('产品编码生成成功');
                    } else {
                        toastr.error('产品编码生成失败');
                    }
                },
                error: function() {
                    toastr.error('产品编码生成失败，请稍后重试');
                },
                complete: function() {
                    // 恢复按钮状态
                    $btn.html(originalHtml);
                    $btn.prop('disabled', false);
                }
            });
        });

        // 表单验证
        $('#productForm').on('submit', function(e) {
            var isValid = true;

            // 验证供应商
            if ($('#supplier_id').val() == '0') {
                isValid = false;
                $('#supplier_id').addClass('is-invalid');
                $('#supplier_id').after('<div class="invalid-feedback">请选择供应商</div>');
            } else {
                $('#supplier_id').removeClass('is-invalid');
            }

            // 验证食材
            if ($('#ingredient_id').val() == '0') {
                isValid = false;
                $('#ingredient_id').addClass('is-invalid');
                $('#ingredient_id').after('<div class="invalid-feedback">请选择食材</div>');
            } else {
                $('#ingredient_id').removeClass('is-invalid');
            }

            // 验证价格
            if (!$('#price').val() || parseFloat($('#price').val()) <= 0) {
                isValid = false;
                $('#price').addClass('is-invalid');
                $('#price').after('<div class="invalid-feedback">请输入有效的价格</div>');
            } else {
                $('#price').removeClass('is-invalid');
            }

            if (!isValid) {
                e.preventDefault();
                $('html, body').animate({
                    scrollTop: $('.is-invalid:first').offset().top - 100
                }, 500);
            }
        });
    });
</script>
{% endblock %}
